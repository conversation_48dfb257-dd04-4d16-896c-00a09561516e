#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel进销存系统结构分析工具
分析2025-3-stock.xlsx文件中所有工作表的结构
"""

import pandas as pd
import openpyxl
from openpyxl.utils import get_column_letter
import json
import os
from datetime import datetime

class ExcelAnalyzer:
    def __init__(self, file_path):
        self.file_path = file_path
        self.workbook = None
        self.analysis_results = {}
        
    def load_workbook(self):
        """加载Excel工作簿"""
        try:
            self.workbook = openpyxl.load_workbook(self.file_path, data_only=True)
            print(f"成功加载工作簿: {self.file_path}")
            return True
        except Exception as e:
            print(f"加载工作簿失败: {e}")
            return False
    
    def get_merged_cells_info(self, worksheet):
        """获取合并单元格信息"""
        merged_cells = []
        for merged_range in worksheet.merged_cells.ranges:
            start_row, start_col = merged_range.min_row, merged_range.min_col
            end_row, end_col = merged_range.max_row, merged_range.max_col
            
            # 获取合并单元格的值
            cell_value = worksheet.cell(start_row, start_col).value
            
            merged_cells.append({
                'range': str(merged_range),
                'start_cell': f"{get_column_letter(start_col)}{start_row}",
                'end_cell': f"{get_column_letter(end_col)}{end_row}",
                'value': cell_value,
                'rows': end_row - start_row + 1,
                'cols': end_col - start_col + 1
            })
        return merged_cells
    
    def analyze_first_5_rows(self, worksheet):
        """分析前5行的结构"""
        first_5_rows = []
        for row_num in range(1, 6):
            row_data = []
            for col_num in range(1, worksheet.max_column + 1):
                cell = worksheet.cell(row_num, col_num)
                cell_info = {
                    'column': get_column_letter(col_num),
                    'value': cell.value,
                    'data_type': str(type(cell.value).__name__),
                    'is_merged': any(cell.coordinate in merged_range for merged_range in worksheet.merged_cells.ranges)
                }
                row_data.append(cell_info)
            first_5_rows.append({
                'row_number': row_num,
                'cells': row_data
            })
        return first_5_rows
    
    def detect_data_start_row(self, worksheet):
        """检测实际数据开始行"""
        # 检查前10行，寻找数据模式
        for row_num in range(1, min(11, worksheet.max_row + 1)):
            row_values = []
            for col_num in range(1, min(10, worksheet.max_column + 1)):
                cell_value = worksheet.cell(row_num, col_num).value
                row_values.append(cell_value)
            
            # 如果这一行有多个非空值且不是明显的标题行，可能是数据开始行
            non_empty_count = sum(1 for v in row_values if v is not None and str(v).strip())
            if non_empty_count >= 3:  # 至少3个非空单元格
                # 检查是否包含数字或日期，这通常表示数据行
                has_numeric = any(isinstance(v, (int, float)) for v in row_values if v is not None)
                if has_numeric:
                    return row_num
        
        return 1  # 默认返回第1行
    
    def classify_row_types(self, worksheet):
        """分类行类型"""
        row_classifications = []
        
        for row_num in range(1, min(21, worksheet.max_row + 1)):  # 分析前20行
            row_values = []
            for col_num in range(1, min(worksheet.max_column + 1, 10)):
                cell_value = worksheet.cell(row_num, col_num).value
                row_values.append(cell_value)
            
            non_empty_count = sum(1 for v in row_values if v is not None and str(v).strip())
            has_numeric = any(isinstance(v, (int, float)) for v in row_values if v is not None)
            has_text = any(isinstance(v, str) and len(str(v).strip()) > 0 for v in row_values if v is not None)
            
            # 分类逻辑
            if non_empty_count == 0:
                row_type = "空行"
            elif non_empty_count == 1 and has_text:
                row_type = "标题行"
            elif has_text and not has_numeric and non_empty_count >= 2:
                row_type = "表头行"
            elif has_numeric and has_text:
                row_type = "数据行"
            elif has_numeric and non_empty_count >= 2:
                row_type = "汇总行"
            else:
                row_type = "其他"
            
            row_classifications.append({
                'row_number': row_num,
                'type': row_type,
                'non_empty_count': non_empty_count,
                'has_numeric': has_numeric,
                'has_text': has_text,
                'sample_values': [str(v)[:20] if v is not None else None for v in row_values[:5]]
            })
        
        return row_classifications
    
    def analyze_worksheet(self, sheet_name):
        """分析单个工作表"""
        print(f"\n正在分析工作表: {sheet_name}")
        worksheet = self.workbook[sheet_name]
        
        # 基本信息
        basic_info = {
            'name': sheet_name,
            'max_row': worksheet.max_row,
            'max_column': worksheet.max_column,
            'dimensions': f"{worksheet.max_column}列 x {worksheet.max_row}行"
        }
        
        # 合并单元格信息
        merged_cells = self.get_merged_cells_info(worksheet)
        
        # 前5行分析
        first_5_rows = self.analyze_first_5_rows(worksheet)
        
        # 数据开始行检测
        data_start_row = self.detect_data_start_row(worksheet)
        
        # 行类型分类
        row_classifications = self.classify_row_types(worksheet)
        
        # 列分析（分析前10列的数据类型）
        column_analysis = []
        for col_num in range(1, min(11, worksheet.max_column + 1)):
            col_letter = get_column_letter(col_num)
            col_values = []
            for row_num in range(data_start_row, min(data_start_row + 10, worksheet.max_row + 1)):
                cell_value = worksheet.cell(row_num, col_num).value
                if cell_value is not None:
                    col_values.append(cell_value)
            
            # 分析列的数据类型
            if col_values:
                data_types = [type(v).__name__ for v in col_values]
                most_common_type = max(set(data_types), key=data_types.count)
                
                column_analysis.append({
                    'column': col_letter,
                    'column_number': col_num,
                    'header': worksheet.cell(1, col_num).value,
                    'most_common_type': most_common_type,
                    'sample_values': [str(v)[:20] for v in col_values[:3]],
                    'non_empty_count': len(col_values)
                })
        
        return {
            'basic_info': basic_info,
            'merged_cells': merged_cells,
            'first_5_rows': first_5_rows,
            'data_start_row': data_start_row,
            'row_classifications': row_classifications,
            'column_analysis': column_analysis
        }
    
    def analyze_all_worksheets(self):
        """分析所有工作表"""
        if not self.load_workbook():
            return False
        
        print(f"工作簿包含 {len(self.workbook.sheetnames)} 个工作表:")
        for i, sheet_name in enumerate(self.workbook.sheetnames, 1):
            print(f"{i}. {sheet_name}")
        
        for sheet_name in self.workbook.sheetnames:
            try:
                self.analysis_results[sheet_name] = self.analyze_worksheet(sheet_name)
            except Exception as e:
                print(f"分析工作表 {sheet_name} 时出错: {e}")
                self.analysis_results[sheet_name] = {'error': str(e)}
        
        return True
    
    def save_markdown_report(self, md_file):
        """生成Markdown格式的分析报告"""
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write("# Excel进销存系统结构分析报告\n\n")
            f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**文件路径**: {self.file_path}\n\n")

            f.write("## 工作表概览\n\n")
            f.write("| 序号 | 工作表名称 | 行数 | 列数 | 数据开始行 |\n")
            f.write("|------|------------|------|------|------------|\n")

            for i, (sheet_name, analysis) in enumerate(self.analysis_results.items(), 1):
                if 'error' not in analysis:
                    basic_info = analysis['basic_info']
                    data_start = analysis['data_start_row']
                    f.write(f"| {i} | {sheet_name} | {basic_info['max_row']} | {basic_info['max_column']} | {data_start} |\n")
                else:
                    f.write(f"| {i} | {sheet_name} | 错误 | 错误 | 错误 |\n")

            f.write("\n")

            # 详细分析每个工作表
            for sheet_name, analysis in self.analysis_results.items():
                f.write(f"## 工作表: {sheet_name}\n\n")

                if 'error' in analysis:
                    f.write(f"**错误**: {analysis['error']}\n\n")
                    continue

                # 基本信息
                basic_info = analysis['basic_info']
                f.write(f"**基本信息**:\n")
                f.write(f"- 维度: {basic_info['dimensions']}\n")
                f.write(f"- 数据开始行: {analysis['data_start_row']}\n\n")

                # 合并单元格
                if analysis['merged_cells']:
                    f.write("**合并单元格**:\n")
                    for merged in analysis['merged_cells']:
                        f.write(f"- {merged['range']}: \"{merged['value']}\" ({merged['rows']}行 x {merged['cols']}列)\n")
                    f.write("\n")

                # 行类型分析
                f.write("**行类型分析** (前20行):\n")
                f.write("| 行号 | 类型 | 非空单元格数 | 示例值 |\n")
                f.write("|------|------|--------------|--------|\n")
                for row_class in analysis['row_classifications']:
                    sample_str = ", ".join([str(v) if v else "空" for v in row_class['sample_values'][:3]])
                    f.write(f"| {row_class['row_number']} | {row_class['type']} | {row_class['non_empty_count']} | {sample_str} |\n")
                f.write("\n")

                # 列分析
                f.write("**列结构分析** (前10列):\n")
                f.write("| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |\n")
                f.write("|----|------|--------------|----------|--------|\n")
                for col in analysis['column_analysis']:
                    header = str(col['header']) if col['header'] else "无"
                    sample_str = ", ".join(col['sample_values'])
                    f.write(f"| {col['column']} | {header} | {col['most_common_type']} | {col['non_empty_count']} | {sample_str} |\n")
                f.write("\n")

    def save_results(self):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存JSON格式
        json_file = f"excel_analysis_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2, default=str)
        print(f"JSON分析结果已保存到: {json_file}")

        # 保存Markdown格式
        md_file = f"excel_analysis_{timestamp}.md"
        self.save_markdown_report(md_file)
        print(f"Markdown分析报告已保存到: {md_file}")

        return json_file, md_file

def main():
    file_path = "2025-3-stock.xlsx"
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    analyzer = ExcelAnalyzer(file_path)
    
    if analyzer.analyze_all_worksheets():
        json_file, md_file = analyzer.save_results()
        print(f"\n分析完成！")
        print(f"结果文件: {json_file}, {md_file}")
    else:
        print("分析失败！")

if __name__ == "__main__":
    main()
