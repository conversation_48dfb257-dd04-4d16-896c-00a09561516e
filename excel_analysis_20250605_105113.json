{"采购计划": {"basic_info": {"name": "采购计划", "max_row": 10669, "max_column": 8, "dimensions": "8列 x 10669行"}, "merged_cells": [{"range": "A1:H1", "start_cell": "A1", "end_cell": "H1", "value": "2025年3月采购计划表", "rows": 1, "cols": 8}, {"range": "A2:F2", "start_cell": "A2", "end_cell": "F2", "value": "总金额：", "rows": 1, "cols": 6}, {"range": "G2:H2", "start_cell": "G2", "end_cell": "H2", "value": 319348.99, "rows": 1, "cols": 2}], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "2025年3月采购计划表", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}]}, {"row_number": 2, "cells": [{"column": "A", "value": "总金额：", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": 319348.99, "data_type": "float", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}]}, {"row_number": 3, "cells": [{"column": "A", "value": "采购日期", "data_type": "str", "is_merged": false}, {"column": "B", "value": "物资名称", "data_type": "str", "is_merged": false}, {"column": "C", "value": "单 位", "data_type": "str", "is_merged": false}, {"column": "D", "value": "单 价", "data_type": "str", "is_merged": false}, {"column": "E", "value": "数 量", "data_type": "str", "is_merged": false}, {"column": "F", "value": "金 额", "data_type": "str", "is_merged": false}, {"column": "G", "value": "备 注", "data_type": "str", "is_merged": false}, {"column": "H", "value": "面粉", "data_type": "str", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "2025-03-01 00:00:00", "data_type": "datetime", "is_merged": false}, {"column": "B", "value": "面粉", "data_type": "str", "is_merged": false}, {"column": "C", "value": "千克", "data_type": "str", "is_merged": false}, {"column": "D", "value": 3.2, "data_type": "float", "is_merged": false}, {"column": "E", "value": 750, "data_type": "int", "is_merged": false}, {"column": "F", "value": 2400, "data_type": "int", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": "面粉", "data_type": "str", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": "2025-03-01 00:00:00", "data_type": "datetime", "is_merged": false}, {"column": "B", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "C", "value": "千克", "data_type": "str", "is_merged": false}, {"column": "D", "value": 4.2, "data_type": "float", "is_merged": false}, {"column": "E", "value": 500, "data_type": "int", "is_merged": false}, {"column": "F", "value": 2100, "data_type": "int", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": "大米", "data_type": "str", "is_merged": false}]}], "data_start_row": 4, "row_classifications": [{"row_number": 1, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["2025年3月采购计划表", null, null, null, null]}, {"row_number": 2, "type": "数据行", "non_empty_count": 2, "has_numeric": true, "has_text": true, "sample_values": ["总金额：", null, null, null, null]}, {"row_number": 3, "type": "表头行", "non_empty_count": 8, "has_numeric": false, "has_text": true, "sample_values": ["采购日期", "物资名称", "单 位", "单 价", "数 量"]}, {"row_number": 4, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-01 00:00:00", "面粉", "千克", "3.2", "750"]}, {"row_number": 5, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-01 00:00:00", "大米", "千克", "4.2", "500"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-01 00:00:00", "植物油", "千克", "7.6", "720"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "五花肉", "千克", "21.09", "53"]}, {"row_number": 8, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "猪排骨", "千克", "23.07", "48"]}, {"row_number": 9, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "牛后腿肉", "千克", "43.89", "105"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "牛排", "千克", "37.2", "33"]}, {"row_number": 11, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "蹄筋", "千克", "35.05", "28"]}, {"row_number": 12, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "羊拐", "千克", "43.71", "38"]}, {"row_number": 13, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "牛腱子肉", "千克", "47.86", "21"]}, {"row_number": 14, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "鸡脯肉", "千克", "12.11", "14"]}, {"row_number": 15, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "鸭肠", "千克", "17.45", "11"]}, {"row_number": 16, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "鸭头", "千克", "21.69", "12"]}, {"row_number": 17, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "鸭掌", "千克", "27.6", "12"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "鸡胗", "千克", "18.03", "25"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "兔肉", "千克", "30.98", "28"]}, {"row_number": 20, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-03 00:00:00", "三黄鸡", "千克", "15.78", "24"]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "2025年3月采购计划表", "most_common_type": "datetime", "sample_values": ["2025-03-01 00:00:00", "2025-03-01 00:00:00", "2025-03-01 00:00:00"], "non_empty_count": 10}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "str", "sample_values": ["面粉", "大米", "植物油"], "non_empty_count": 10}, {"column": "C", "column_number": 3, "header": null, "most_common_type": "str", "sample_values": ["千克", "千克", "千克"], "non_empty_count": 10}, {"column": "D", "column_number": 4, "header": null, "most_common_type": "float", "sample_values": ["3.2", "4.2", "7.6"], "non_empty_count": 10}, {"column": "E", "column_number": 5, "header": null, "most_common_type": "int", "sample_values": ["750", "500", "720"], "non_empty_count": 10}, {"column": "F", "column_number": 6, "header": null, "most_common_type": "float", "sample_values": ["2400", "2100", "5472"], "non_empty_count": 10}, {"column": "H", "column_number": 8, "header": null, "most_common_type": "str", "sample_values": ["面粉", "大米", "植物油"], "non_empty_count": 10}]}, "上月库存": {"basic_info": {"name": "上月库存", "max_row": 1590, "max_column": 6, "dimensions": "6列 x 1590行"}, "merged_cells": [{"range": "A1:F1", "start_cell": "A1", "end_cell": "F1", "value": "2025年2月库存结余表", "rows": 1, "cols": 6}, {"range": "A2:E2", "start_cell": "A2", "end_cell": "E2", "value": "总金额：", "rows": 1, "cols": 5}], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "2025年2月库存结余表", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}]}, {"row_number": 2, "cells": [{"column": "A", "value": "总金额：", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": 106632.2, "data_type": "float", "is_merged": false}]}, {"row_number": 3, "cells": [{"column": "A", "value": "日期", "data_type": "str", "is_merged": false}, {"column": "B", "value": "物资名称", "data_type": "str", "is_merged": false}, {"column": "C", "value": "单位", "data_type": "str", "is_merged": false}, {"column": "D", "value": "单价", "data_type": "str", "is_merged": false}, {"column": "E", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "F", "value": "金额", "data_type": "str", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "2024-11-15 00:00:00", "data_type": "datetime", "is_merged": false}, {"column": "B", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "C", "value": "千克", "data_type": "str", "is_merged": false}, {"column": "D", "value": 4.2, "data_type": "float", "is_merged": false}, {"column": "E", "value": 297, "data_type": "int", "is_merged": false}, {"column": "F", "value": 1247.4, "data_type": "float", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": "2025-01-01 00:00:00", "data_type": "datetime", "is_merged": false}, {"column": "B", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "C", "value": "千克", "data_type": "str", "is_merged": false}, {"column": "D", "value": 4.2, "data_type": "float", "is_merged": false}, {"column": "E", "value": 625, "data_type": "int", "is_merged": false}, {"column": "F", "value": 2625, "data_type": "int", "is_merged": false}]}], "data_start_row": 4, "row_classifications": [{"row_number": 1, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["2025年2月库存结余表", null, null, null, null]}, {"row_number": 2, "type": "数据行", "non_empty_count": 2, "has_numeric": true, "has_text": true, "sample_values": ["总金额：", null, null, null, null]}, {"row_number": 3, "type": "表头行", "non_empty_count": 6, "has_numeric": false, "has_text": true, "sample_values": ["日期", "物资名称", "单位", "单价", "数量"]}, {"row_number": 4, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2024-11-15 00:00:00", "大米", "千克", "4.2", "297"]}, {"row_number": 5, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-01-01 00:00:00", "大米", "千克", "4.2", "625"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-27 00:00:00", "大米", "千克", "4.2", "1375"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-28 00:00:00", "西红柿", "千克", "5.04", "15"]}, {"row_number": 8, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-24 00:00:00", "白萝卜", "千克", "2.09", "9"]}, {"row_number": 9, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-28 00:00:00", "白萝卜", "千克", "2.09", "7"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-26 00:00:00", "桂皮", "千克", "29.5", "1"]}, {"row_number": 11, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2024-12-18 00:00:00", "小米", "千克", "8.4", "1"]}, {"row_number": 12, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-01-27 00:00:00", "小米", "千克", "8.4", "2"]}, {"row_number": 13, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-12 00:00:00", "小米", "千克", "8.4", "12"]}, {"row_number": 14, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-26 00:00:00", "小米", "千克", "8.4", "3"]}, {"row_number": 15, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2024-08-27 00:00:00", "糯米", "千克", "9.28", "4"]}, {"row_number": 16, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2024-12-04 00:00:00", "糯米", "千克", "7", "5"]}, {"row_number": 17, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-26 00:00:00", "糯米", "千克", "7", "5"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2024-04-30 00:00:00", "绿豆", "千克", "10", "58"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2024-04-30 00:00:00", "冰淇淋粉（香芋）", "千克", "26.4", "1"]}, {"row_number": 20, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2024-04-30 00:00:00", "冰淇淋粉（草莓）", "千克", "26.4", "1"]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "2025年2月库存结余表", "most_common_type": "datetime", "sample_values": ["2024-11-15 00:00:00", "2025-01-01 00:00:00", "2025-02-27 00:00:00"], "non_empty_count": 10}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "str", "sample_values": ["大米", "大米", "大米"], "non_empty_count": 10}, {"column": "C", "column_number": 3, "header": null, "most_common_type": "str", "sample_values": ["千克", "千克", "千克"], "non_empty_count": 10}, {"column": "D", "column_number": 4, "header": null, "most_common_type": "float", "sample_values": ["4.2", "4.2", "4.2"], "non_empty_count": 10}, {"column": "E", "column_number": 5, "header": null, "most_common_type": "int", "sample_values": ["297", "625", "1375"], "non_empty_count": 10}, {"column": "F", "column_number": 6, "header": null, "most_common_type": "float", "sample_values": ["1247.4", "2625", "5775"], "non_empty_count": 10}]}, "库存结余": {"basic_info": {"name": "库存结余", "max_row": 1590, "max_column": 6, "dimensions": "6列 x 1590行"}, "merged_cells": [{"range": "A1:F1", "start_cell": "A1", "end_cell": "F1", "value": "2025年3月库存结余表", "rows": 1, "cols": 6}, {"range": "A2:E2", "start_cell": "A2", "end_cell": "E2", "value": "总金额：", "rows": 1, "cols": 5}], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "2025年3月库存结余表", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}]}, {"row_number": 2, "cells": [{"column": "A", "value": "总金额：", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": 103696.89, "data_type": "float", "is_merged": false}]}, {"row_number": 3, "cells": [{"column": "A", "value": "日期", "data_type": "str", "is_merged": false}, {"column": "B", "value": "物资名称", "data_type": "str", "is_merged": false}, {"column": "C", "value": "单位", "data_type": "str", "is_merged": false}, {"column": "D", "value": "单价", "data_type": "str", "is_merged": false}, {"column": "E", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "F", "value": "金额", "data_type": "str", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "2025-01-01 00:00:00", "data_type": "datetime", "is_merged": false}, {"column": "B", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "C", "value": "千克", "data_type": "str", "is_merged": false}, {"column": "D", "value": 4.2, "data_type": "float", "is_merged": false}, {"column": "E", "value": 94, "data_type": "int", "is_merged": false}, {"column": "F", "value": 394.8, "data_type": "float", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": "2025-02-27 00:00:00", "data_type": "datetime", "is_merged": false}, {"column": "B", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "C", "value": "千克", "data_type": "str", "is_merged": false}, {"column": "D", "value": 4.2, "data_type": "float", "is_merged": false}, {"column": "E", "value": 1375, "data_type": "int", "is_merged": false}, {"column": "F", "value": 5775, "data_type": "int", "is_merged": false}]}], "data_start_row": 4, "row_classifications": [{"row_number": 1, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["2025年3月库存结余表", null, null, null, null]}, {"row_number": 2, "type": "数据行", "non_empty_count": 2, "has_numeric": true, "has_text": true, "sample_values": ["总金额：", null, null, null, null]}, {"row_number": 3, "type": "表头行", "non_empty_count": 6, "has_numeric": false, "has_text": true, "sample_values": ["日期", "物资名称", "单位", "单价", "数量"]}, {"row_number": 4, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-01-01 00:00:00", "大米", "千克", "4.2", "94"]}, {"row_number": 5, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-27 00:00:00", "大米", "千克", "4.2", "1375"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-01 00:00:00", "大米", "千克", "4.2", "500"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-28 00:00:00", "西红柿", "千克", "5.32", "5"]}, {"row_number": 8, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-31 00:00:00", "西红柿", "千克", "5.32", "24"]}, {"row_number": 9, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-21 00:00:00", "白萝卜", "千克", "2.09", "1"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-24 00:00:00", "白萝卜", "千克", "1.81", "8"]}, {"row_number": 11, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-26 00:00:00", "白萝卜", "千克", "1.81", "2"]}, {"row_number": 12, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-28 00:00:00", "白萝卜", "千克", "1.81", "8"]}, {"row_number": 13, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-31 00:00:00", "白萝卜", "千克", "1.81", "5"]}, {"row_number": 14, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-26 00:00:00", "小米", "千克", "8.4", "3"]}, {"row_number": 15, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-12 00:00:00", "小米", "千克", "8.4", "12"]}, {"row_number": 16, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-03-26 00:00:00", "小米", "千克", "8.4", "10"]}, {"row_number": 17, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2024-12-04 00:00:00", "糯米", "千克", "7", "3"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2025-02-26 00:00:00", "糯米", "千克", "7", "5"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2024-04-30 00:00:00", "绿豆", "千克", "10", "58"]}, {"row_number": 20, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["2024-04-30 00:00:00", "冰淇淋粉（香芋）", "千克", "26.4", "1"]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "2025年3月库存结余表", "most_common_type": "datetime", "sample_values": ["2025-01-01 00:00:00", "2025-02-27 00:00:00", "2025-03-01 00:00:00"], "non_empty_count": 10}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "str", "sample_values": ["大米", "大米", "大米"], "non_empty_count": 10}, {"column": "C", "column_number": 3, "header": null, "most_common_type": "str", "sample_values": ["千克", "千克", "千克"], "non_empty_count": 10}, {"column": "D", "column_number": 4, "header": null, "most_common_type": "float", "sample_values": ["4.2", "4.2", "4.2"], "non_empty_count": 10}, {"column": "E", "column_number": 5, "header": null, "most_common_type": "int", "sample_values": ["94", "1375", "500"], "non_empty_count": 10}, {"column": "F", "column_number": 6, "header": null, "most_common_type": "float", "sample_values": ["394.8", "5775", "2100"], "non_empty_count": 10}]}, "出入库登记本": {"basic_info": {"name": "出入库登记本", "max_row": 365, "max_column": 159, "dimensions": "159列 x 365行"}, "merged_cells": [{"range": "A1:AM1", "start_cell": "A1", "end_cell": "AM1", "value": "出入库登记本", "rows": 1, "cols": 39}, {"range": "AN1:BV1", "start_cell": "AN1", "end_cell": "BV1", "value": "出入库登记本", "rows": 1, "cols": 35}, {"range": "BW1:DE1", "start_cell": "BW1", "end_cell": "DE1", "value": "出入库登记本", "rows": 1, "cols": 35}, {"range": "DF1:EN1", "start_cell": "DF1", "end_cell": "EN1", "value": "出入库登记本", "rows": 1, "cols": 35}, {"range": "EO1:FC1", "start_cell": "EO1", "end_cell": "FC1", "value": "出入库登记本", "rows": 1, "cols": 15}, {"range": "A2:AM2", "start_cell": "A2", "end_cell": "AM2", "value": "单位：千克、元", "rows": 1, "cols": 39}, {"range": "AN2:BV2", "start_cell": "AN2", "end_cell": "BV2", "value": "单位：千克、元", "rows": 1, "cols": 35}, {"range": "BW2:DE2", "start_cell": "BW2", "end_cell": "DE2", "value": "单位：千克、元", "rows": 1, "cols": 35}, {"range": "DF2:EN2", "start_cell": "DF2", "end_cell": "EN2", "value": "单位：千克、元", "rows": 1, "cols": 35}, {"range": "EO2:FC2", "start_cell": "EO2", "end_cell": "FC2", "value": "单位：千克、元", "rows": 1, "cols": 15}, {"range": "E3:I3", "start_cell": "E3", "end_cell": "I3", "value": 45717, "rows": 1, "cols": 5}, {"range": "J3:N3", "start_cell": "J3", "end_cell": "N3", "value": 45718, "rows": 1, "cols": 5}, {"range": "O3:S3", "start_cell": "O3", "end_cell": "S3", "value": 45719, "rows": 1, "cols": 5}, {"range": "T3:X3", "start_cell": "T3", "end_cell": "X3", "value": 45720, "rows": 1, "cols": 5}, {"range": "Y3:AC3", "start_cell": "Y3", "end_cell": "AC3", "value": 45721, "rows": 1, "cols": 5}, {"range": "AD3:AH3", "start_cell": "AD3", "end_cell": "AH3", "value": 45722, "rows": 1, "cols": 5}, {"range": "AI3:AM3", "start_cell": "AI3", "end_cell": "AM3", "value": 45723, "rows": 1, "cols": 5}, {"range": "AN3:AR3", "start_cell": "AN3", "end_cell": "AR3", "value": 45724, "rows": 1, "cols": 5}, {"range": "AS3:AW3", "start_cell": "AS3", "end_cell": "AW3", "value": 45725, "rows": 1, "cols": 5}, {"range": "AX3:BB3", "start_cell": "AX3", "end_cell": "BB3", "value": 45726, "rows": 1, "cols": 5}, {"range": "BC3:BG3", "start_cell": "BC3", "end_cell": "BG3", "value": 45727, "rows": 1, "cols": 5}, {"range": "BH3:BL3", "start_cell": "BH3", "end_cell": "BL3", "value": 45728, "rows": 1, "cols": 5}, {"range": "BM3:BQ3", "start_cell": "BM3", "end_cell": "BQ3", "value": 45729, "rows": 1, "cols": 5}, {"range": "BR3:BV3", "start_cell": "BR3", "end_cell": "BV3", "value": 45730, "rows": 1, "cols": 5}, {"range": "BW3:CA3", "start_cell": "BW3", "end_cell": "CA3", "value": 45731, "rows": 1, "cols": 5}, {"range": "CB3:CF3", "start_cell": "CB3", "end_cell": "CF3", "value": 45732, "rows": 1, "cols": 5}, {"range": "CG3:CK3", "start_cell": "CG3", "end_cell": "CK3", "value": 45733, "rows": 1, "cols": 5}, {"range": "CL3:CP3", "start_cell": "CL3", "end_cell": "CP3", "value": 45734, "rows": 1, "cols": 5}, {"range": "CQ3:CU3", "start_cell": "CQ3", "end_cell": "CU3", "value": 45735, "rows": 1, "cols": 5}, {"range": "CV3:CZ3", "start_cell": "CV3", "end_cell": "CZ3", "value": 45736, "rows": 1, "cols": 5}, {"range": "DA3:DE3", "start_cell": "DA3", "end_cell": "DE3", "value": 45737, "rows": 1, "cols": 5}, {"range": "DF3:DJ3", "start_cell": "DF3", "end_cell": "DJ3", "value": 45738, "rows": 1, "cols": 5}, {"range": "DK3:DO3", "start_cell": "DK3", "end_cell": "DO3", "value": 45739, "rows": 1, "cols": 5}, {"range": "DP3:DT3", "start_cell": "DP3", "end_cell": "DT3", "value": 45740, "rows": 1, "cols": 5}, {"range": "DU3:DY3", "start_cell": "DU3", "end_cell": "DY3", "value": 45741, "rows": 1, "cols": 5}, {"range": "DZ3:ED3", "start_cell": "DZ3", "end_cell": "ED3", "value": 45742, "rows": 1, "cols": 5}, {"range": "EE3:EI3", "start_cell": "EE3", "end_cell": "EI3", "value": 45743, "rows": 1, "cols": 5}, {"range": "EJ3:EN3", "start_cell": "EJ3", "end_cell": "EN3", "value": 45744, "rows": 1, "cols": 5}, {"range": "EO3:ES3", "start_cell": "EO3", "end_cell": "ES3", "value": 45745, "rows": 1, "cols": 5}, {"range": "ET3:EX3", "start_cell": "ET3", "end_cell": "EX3", "value": 45746, "rows": 1, "cols": 5}, {"range": "EY3:FC3", "start_cell": "EY3", "end_cell": "FC3", "value": 45747, "rows": 1, "cols": 5}, {"range": "A3:A4", "start_cell": "A3", "end_cell": "A4", "value": "序号", "rows": 2, "cols": 1}, {"range": "B3:B4", "start_cell": "B3", "end_cell": "B4", "value": "品名", "rows": 2, "cols": 1}, {"range": "C3:C4", "start_cell": "C3", "end_cell": "C4", "value": "分类", "rows": 2, "cols": 1}, {"range": "D3:D4", "start_cell": "D3", "end_cell": "D4", "value": "总出库", "rows": 2, "cols": 1}], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "出入库登记本", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AN", "value": "出入库登记本", "data_type": "str", "is_merged": true}, {"column": "AO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BW", "value": "出入库登记本", "data_type": "str", "is_merged": true}, {"column": "BX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DF", "value": "出入库登记本", "data_type": "str", "is_merged": true}, {"column": "DG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ED", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EO", "value": "出入库登记本", "data_type": "str", "is_merged": true}, {"column": "EP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ER", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ES", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ET", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "FA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "FB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "FC", "value": null, "data_type": "NoneType", "is_merged": true}]}, {"row_number": 2, "cells": [{"column": "A", "value": "单位：千克、元", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AN", "value": "单位：千克、元", "data_type": "str", "is_merged": true}, {"column": "AO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BW", "value": "单位：千克、元", "data_type": "str", "is_merged": true}, {"column": "BX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DF", "value": "单位：千克、元", "data_type": "str", "is_merged": true}, {"column": "DG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ED", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EO", "value": "单位：千克、元", "data_type": "str", "is_merged": true}, {"column": "EP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ER", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ES", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ET", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "FA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "FB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "FC", "value": null, "data_type": "NoneType", "is_merged": true}]}, {"row_number": 3, "cells": [{"column": "A", "value": "序号", "data_type": "str", "is_merged": true}, {"column": "B", "value": "品名", "data_type": "str", "is_merged": true}, {"column": "C", "value": "分类", "data_type": "str", "is_merged": true}, {"column": "D", "value": "总出库", "data_type": "str", "is_merged": true}, {"column": "E", "value": 45717, "data_type": "int", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "J", "value": 45718, "data_type": "int", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": 45719, "data_type": "int", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": 45720, "data_type": "int", "is_merged": true}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": 45721, "data_type": "int", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AD", "value": 45722, "data_type": "int", "is_merged": true}, {"column": "AE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AI", "value": 45723, "data_type": "int", "is_merged": true}, {"column": "AJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AN", "value": 45724, "data_type": "int", "is_merged": true}, {"column": "AO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AS", "value": 45725, "data_type": "int", "is_merged": true}, {"column": "AT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AX", "value": 45726, "data_type": "int", "is_merged": true}, {"column": "AY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "AZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BC", "value": 45727, "data_type": "int", "is_merged": true}, {"column": "BD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BH", "value": 45728, "data_type": "int", "is_merged": true}, {"column": "BI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BM", "value": 45729, "data_type": "int", "is_merged": true}, {"column": "BN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BR", "value": 45730, "data_type": "int", "is_merged": true}, {"column": "BS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BW", "value": 45731, "data_type": "int", "is_merged": true}, {"column": "BX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "BZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CB", "value": 45732, "data_type": "int", "is_merged": true}, {"column": "CC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CG", "value": 45733, "data_type": "int", "is_merged": true}, {"column": "CH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CL", "value": 45734, "data_type": "int", "is_merged": true}, {"column": "CM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CQ", "value": 45735, "data_type": "int", "is_merged": true}, {"column": "CR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CV", "value": 45736, "data_type": "int", "is_merged": true}, {"column": "CW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "CZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DA", "value": 45737, "data_type": "int", "is_merged": true}, {"column": "DB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DD", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DE", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DF", "value": 45738, "data_type": "int", "is_merged": true}, {"column": "DG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DJ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DK", "value": 45739, "data_type": "int", "is_merged": true}, {"column": "DL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DO", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DP", "value": 45740, "data_type": "int", "is_merged": true}, {"column": "DQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DR", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DS", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DT", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DU", "value": 45741, "data_type": "int", "is_merged": true}, {"column": "DV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DY", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "DZ", "value": 45742, "data_type": "int", "is_merged": true}, {"column": "EA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EC", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ED", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EE", "value": 45743, "data_type": "int", "is_merged": true}, {"column": "EF", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EG", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EH", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EI", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EJ", "value": 45744, "data_type": "int", "is_merged": true}, {"column": "EK", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EL", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EM", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EN", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EO", "value": 45745, "data_type": "int", "is_merged": true}, {"column": "EP", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EQ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ER", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ES", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "ET", "value": 45746, "data_type": "int", "is_merged": true}, {"column": "EU", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EV", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EW", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EX", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "EY", "value": 45747, "data_type": "int", "is_merged": true}, {"column": "EZ", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "FA", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "FB", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "FC", "value": null, "data_type": "NoneType", "is_merged": true}]}, {"row_number": 4, "cells": [{"column": "A", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "F", "value": "早", "data_type": "str", "is_merged": false}, {"column": "G", "value": "中", "data_type": "str", "is_merged": false}, {"column": "H", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "I", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "J", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "K", "value": "早", "data_type": "str", "is_merged": false}, {"column": "L", "value": "中", "data_type": "str", "is_merged": false}, {"column": "M", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "N", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "O", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "P", "value": "早", "data_type": "str", "is_merged": false}, {"column": "Q", "value": "中", "data_type": "str", "is_merged": false}, {"column": "R", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "S", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "T", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "U", "value": "早", "data_type": "str", "is_merged": false}, {"column": "V", "value": "中", "data_type": "str", "is_merged": false}, {"column": "W", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "X", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "Y", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "Z", "value": "早", "data_type": "str", "is_merged": false}, {"column": "AA", "value": "中", "data_type": "str", "is_merged": false}, {"column": "AB", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "AC", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "AD", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "AE", "value": "早", "data_type": "str", "is_merged": false}, {"column": "AF", "value": "中", "data_type": "str", "is_merged": false}, {"column": "AG", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "AH", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "AI", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "AJ", "value": "早", "data_type": "str", "is_merged": false}, {"column": "AK", "value": "中", "data_type": "str", "is_merged": false}, {"column": "AL", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "AM", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "AN", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "AO", "value": "早", "data_type": "str", "is_merged": false}, {"column": "AP", "value": "中", "data_type": "str", "is_merged": false}, {"column": "AQ", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "AR", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "AS", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "AT", "value": "早", "data_type": "str", "is_merged": false}, {"column": "AU", "value": "中", "data_type": "str", "is_merged": false}, {"column": "AV", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "AW", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "AX", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "AY", "value": "早", "data_type": "str", "is_merged": false}, {"column": "AZ", "value": "中", "data_type": "str", "is_merged": false}, {"column": "BA", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "BB", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "BC", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "BD", "value": "早", "data_type": "str", "is_merged": false}, {"column": "BE", "value": "中", "data_type": "str", "is_merged": false}, {"column": "BF", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "BG", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "BH", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "BI", "value": "早", "data_type": "str", "is_merged": false}, {"column": "BJ", "value": "中", "data_type": "str", "is_merged": false}, {"column": "BK", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "BL", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "BM", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "BN", "value": "早", "data_type": "str", "is_merged": false}, {"column": "BO", "value": "中", "data_type": "str", "is_merged": false}, {"column": "BP", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "BQ", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "BR", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "BS", "value": "早", "data_type": "str", "is_merged": false}, {"column": "BT", "value": "中", "data_type": "str", "is_merged": false}, {"column": "BU", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "BV", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "BW", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "BX", "value": "早", "data_type": "str", "is_merged": false}, {"column": "BY", "value": "中", "data_type": "str", "is_merged": false}, {"column": "BZ", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "CA", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "CB", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "CC", "value": "早", "data_type": "str", "is_merged": false}, {"column": "CD", "value": "中", "data_type": "str", "is_merged": false}, {"column": "CE", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "CF", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "CG", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "CH", "value": "早", "data_type": "str", "is_merged": false}, {"column": "CI", "value": "中", "data_type": "str", "is_merged": false}, {"column": "CJ", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "CK", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "CL", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "CM", "value": "早", "data_type": "str", "is_merged": false}, {"column": "CN", "value": "中", "data_type": "str", "is_merged": false}, {"column": "CO", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "CP", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "CQ", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "CR", "value": "早", "data_type": "str", "is_merged": false}, {"column": "CS", "value": "中", "data_type": "str", "is_merged": false}, {"column": "CT", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "CU", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "CV", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "CW", "value": "早", "data_type": "str", "is_merged": false}, {"column": "CX", "value": "中", "data_type": "str", "is_merged": false}, {"column": "CY", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "CZ", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "DA", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "DB", "value": "早", "data_type": "str", "is_merged": false}, {"column": "DC", "value": "中", "data_type": "str", "is_merged": false}, {"column": "DD", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "DE", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "DF", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "DG", "value": "早", "data_type": "str", "is_merged": false}, {"column": "DH", "value": "中", "data_type": "str", "is_merged": false}, {"column": "DI", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "DJ", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "DK", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "DL", "value": "早", "data_type": "str", "is_merged": false}, {"column": "DM", "value": "中", "data_type": "str", "is_merged": false}, {"column": "DN", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "DO", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "DP", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "DQ", "value": "早", "data_type": "str", "is_merged": false}, {"column": "DR", "value": "中", "data_type": "str", "is_merged": false}, {"column": "DS", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "DT", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "DU", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "DV", "value": "早", "data_type": "str", "is_merged": false}, {"column": "DW", "value": "中", "data_type": "str", "is_merged": false}, {"column": "DX", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "DY", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "DZ", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "EA", "value": "早", "data_type": "str", "is_merged": false}, {"column": "EB", "value": "中", "data_type": "str", "is_merged": false}, {"column": "EC", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "ED", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "EE", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "EF", "value": "早", "data_type": "str", "is_merged": false}, {"column": "EG", "value": "中", "data_type": "str", "is_merged": false}, {"column": "EH", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "EI", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "EJ", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "EK", "value": "早", "data_type": "str", "is_merged": false}, {"column": "EL", "value": "中", "data_type": "str", "is_merged": false}, {"column": "EM", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "EN", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "EO", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "EP", "value": "早", "data_type": "str", "is_merged": false}, {"column": "EQ", "value": "中", "data_type": "str", "is_merged": false}, {"column": "ER", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "ES", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "ET", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "EU", "value": "早", "data_type": "str", "is_merged": false}, {"column": "EV", "value": "中", "data_type": "str", "is_merged": false}, {"column": "EW", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "EX", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "EY", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "EZ", "value": "早", "data_type": "str", "is_merged": false}, {"column": "FA", "value": "中", "data_type": "str", "is_merged": false}, {"column": "FB", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "FC", "value": "库存", "data_type": "str", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": 1, "data_type": "int", "is_merged": false}, {"column": "B", "value": "白萝卜", "data_type": "str", "is_merged": false}, {"column": "C", "value": "蔬菜", "data_type": "str", "is_merged": false}, {"column": "D", "value": 74, "data_type": "int", "is_merged": false}, {"column": "E", "value": 16, "data_type": "int", "is_merged": false}, {"column": "F", "value": 0, "data_type": "int", "is_merged": false}, {"column": "G", "value": 0, "data_type": "int", "is_merged": false}, {"column": "H", "value": 0, "data_type": "int", "is_merged": false}, {"column": "I", "value": 16, "data_type": "int", "is_merged": false}, {"column": "J", "value": 0, "data_type": "int", "is_merged": false}, {"column": "K", "value": 0, "data_type": "int", "is_merged": false}, {"column": "L", "value": 0, "data_type": "int", "is_merged": false}, {"column": "M", "value": 0, "data_type": "int", "is_merged": false}, {"column": "N", "value": 16, "data_type": "int", "is_merged": false}, {"column": "O", "value": 13, "data_type": "int", "is_merged": false}, {"column": "P", "value": 0, "data_type": "int", "is_merged": false}, {"column": "Q", "value": 0, "data_type": "int", "is_merged": false}, {"column": "R", "value": 0, "data_type": "int", "is_merged": false}, {"column": "S", "value": 29, "data_type": "int", "is_merged": false}, {"column": "T", "value": 0, "data_type": "int", "is_merged": false}, {"column": "U", "value": 0, "data_type": "int", "is_merged": false}, {"column": "V", "value": 0, "data_type": "int", "is_merged": false}, {"column": "W", "value": 0, "data_type": "int", "is_merged": false}, {"column": "X", "value": 29, "data_type": "int", "is_merged": false}, {"column": "Y", "value": 0, "data_type": "int", "is_merged": false}, {"column": "Z", "value": 9, "data_type": "int", "is_merged": false}, {"column": "AA", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AB", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AC", "value": 20, "data_type": "int", "is_merged": false}, {"column": "AD", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AE", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AF", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AG", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AH", "value": 20, "data_type": "int", "is_merged": false}, {"column": "AI", "value": 7, "data_type": "int", "is_merged": false}, {"column": "AJ", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AK", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AL", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AM", "value": 27, "data_type": "int", "is_merged": false}, {"column": "AN", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AO", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AP", "value": 9, "data_type": "int", "is_merged": false}, {"column": "AQ", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AR", "value": 18, "data_type": "int", "is_merged": false}, {"column": "AS", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AT", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AU", "value": 9, "data_type": "int", "is_merged": false}, {"column": "AV", "value": 0, "data_type": "int", "is_merged": false}, {"column": "AW", "value": 9, "data_type": "int", "is_merged": false}, {"column": "AX", "value": 12, "data_type": "int", "is_merged": false}, {"column": "AY", "value": 6, "data_type": "int", "is_merged": false}, {"column": "AZ", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BA", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BB", "value": 15, "data_type": "int", "is_merged": false}, {"column": "BC", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BD", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BE", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BF", "value": 5, "data_type": "int", "is_merged": false}, {"column": "BG", "value": 10, "data_type": "int", "is_merged": false}, {"column": "BH", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BI", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BJ", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BK", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BL", "value": 10, "data_type": "int", "is_merged": false}, {"column": "BM", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BN", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BO", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BP", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BQ", "value": 10, "data_type": "int", "is_merged": false}, {"column": "BR", "value": 11, "data_type": "int", "is_merged": false}, {"column": "BS", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BT", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BU", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BV", "value": 21, "data_type": "int", "is_merged": false}, {"column": "BW", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BX", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BY", "value": 0, "data_type": "int", "is_merged": false}, {"column": "BZ", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CA", "value": 21, "data_type": "int", "is_merged": false}, {"column": "CB", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CC", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CD", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CE", "value": 7, "data_type": "int", "is_merged": false}, {"column": "CF", "value": 14, "data_type": "int", "is_merged": false}, {"column": "CG", "value": 12, "data_type": "int", "is_merged": false}, {"column": "CH", "value": 6, "data_type": "int", "is_merged": false}, {"column": "CI", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CJ", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CK", "value": 20, "data_type": "int", "is_merged": false}, {"column": "CL", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CM", "value": 6, "data_type": "int", "is_merged": false}, {"column": "CN", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CO", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CP", "value": 14, "data_type": "int", "is_merged": false}, {"column": "CQ", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CR", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CS", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CT", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CU", "value": 14, "data_type": "int", "is_merged": false}, {"column": "CV", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CW", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CX", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CY", "value": 0, "data_type": "int", "is_merged": false}, {"column": "CZ", "value": 14, "data_type": "int", "is_merged": false}, {"column": "DA", "value": 4, "data_type": "int", "is_merged": false}, {"column": "DB", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DC", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DD", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DE", "value": 18, "data_type": "int", "is_merged": false}, {"column": "DF", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DG", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DH", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DI", "value": 6, "data_type": "int", "is_merged": false}, {"column": "DJ", "value": 12, "data_type": "int", "is_merged": false}, {"column": "DK", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DL", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DM", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DN", "value": 6, "data_type": "int", "is_merged": false}, {"column": "DO", "value": 6, "data_type": "int", "is_merged": false}, {"column": "DP", "value": 8, "data_type": "int", "is_merged": false}, {"column": "DQ", "value": 5, "data_type": "int", "is_merged": false}, {"column": "DR", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DS", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DT", "value": 9, "data_type": "int", "is_merged": false}, {"column": "DU", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DV", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DW", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DX", "value": 0, "data_type": "int", "is_merged": false}, {"column": "DY", "value": 9, "data_type": "int", "is_merged": false}, {"column": "DZ", "value": 2, "data_type": "int", "is_merged": false}, {"column": "EA", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EB", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EC", "value": 0, "data_type": "int", "is_merged": false}, {"column": "ED", "value": 11, "data_type": "int", "is_merged": false}, {"column": "EE", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EF", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EG", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EH", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EI", "value": 11, "data_type": "int", "is_merged": false}, {"column": "EJ", "value": 8, "data_type": "int", "is_merged": false}, {"column": "EK", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EL", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EM", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EN", "value": 19, "data_type": "int", "is_merged": false}, {"column": "EO", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EP", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EQ", "value": 0, "data_type": "int", "is_merged": false}, {"column": "ER", "value": 0, "data_type": "int", "is_merged": false}, {"column": "ES", "value": 19, "data_type": "int", "is_merged": false}, {"column": "ET", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EU", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EV", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EW", "value": 0, "data_type": "int", "is_merged": false}, {"column": "EX", "value": 19, "data_type": "int", "is_merged": false}, {"column": "EY", "value": 5, "data_type": "int", "is_merged": false}, {"column": "EZ", "value": 0, "data_type": "int", "is_merged": false}, {"column": "FA", "value": 0, "data_type": "int", "is_merged": false}, {"column": "FB", "value": 0, "data_type": "int", "is_merged": false}, {"column": "FC", "value": 24, "data_type": "int", "is_merged": false}]}], "data_start_row": 3, "row_classifications": [{"row_number": 1, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["出入库登记本", null, null, null, null]}, {"row_number": 2, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["单位：千克、元", null, null, null, null]}, {"row_number": 3, "type": "数据行", "non_empty_count": 5, "has_numeric": true, "has_text": true, "sample_values": ["序号", "品名", "分类", "总出库", "45717"]}, {"row_number": 4, "type": "表头行", "non_empty_count": 5, "has_numeric": false, "has_text": true, "sample_values": [null, null, null, null, "入库"]}, {"row_number": 5, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["1", "白萝卜", "蔬菜", "74", "16"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["2", "五花肉", "畜肉", "327", "45"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["3", "土豆", "蔬菜", "543", "32"]}, {"row_number": 8, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["4", "大白菜", "蔬菜", "121", "10"]}, {"row_number": 9, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["5", "黄瓜", "蔬菜", "143", "29"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["6", "鸡蛋", "蛋类", "953", "316"]}, {"row_number": 11, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["7", "去皮大蒜", "蔬菜", "222", "22"]}, {"row_number": 12, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["8", "伊利纯牛奶", "奶及奶制品", "120", "0"]}, {"row_number": 13, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["9", "清真鸡肉肠", "其他", "27", "18"]}, {"row_number": 14, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["10", "清真牛肉肠", "其他", "26", "17"]}, {"row_number": 15, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["11", "松花鸡蛋", "蛋类", "10", "5"]}, {"row_number": 16, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["12", "乌江海带丝", "其他", "284", "65"]}, {"row_number": 17, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["13", "羊后腿", "畜肉", "183", "10"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["14", "毛肚", "畜肉", "90", "0"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["15", "白象汤好喝桶面", "其他", "4", "20"]}, {"row_number": 20, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["16", "白象汤好喝袋面", "其他", "10", "20"]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "出入库登记本", "most_common_type": "int", "sample_values": ["序号", "1", "2"], "non_empty_count": 9}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "str", "sample_values": ["品名", "白萝卜", "五花肉"], "non_empty_count": 9}, {"column": "C", "column_number": 3, "header": null, "most_common_type": "str", "sample_values": ["分类", "蔬菜", "畜肉"], "non_empty_count": 9}, {"column": "D", "column_number": 4, "header": null, "most_common_type": "int", "sample_values": ["总出库", "74", "327"], "non_empty_count": 9}, {"column": "E", "column_number": 5, "header": null, "most_common_type": "int", "sample_values": ["45717", "入库", "16"], "non_empty_count": 10}, {"column": "F", "column_number": 6, "header": null, "most_common_type": "int", "sample_values": ["早", "0", "0"], "non_empty_count": 9}, {"column": "G", "column_number": 7, "header": null, "most_common_type": "int", "sample_values": ["中", "0", "0"], "non_empty_count": 9}, {"column": "H", "column_number": 8, "header": null, "most_common_type": "int", "sample_values": ["晚", "0", "15"], "non_empty_count": 9}, {"column": "I", "column_number": 9, "header": null, "most_common_type": "int", "sample_values": ["库存", "16", "30"], "non_empty_count": 9}, {"column": "J", "column_number": 10, "header": null, "most_common_type": "int", "sample_values": ["45718", "入库", "0"], "non_empty_count": 10}]}, "出库汇总表": {"basic_info": {"name": "出库汇总表", "max_row": 419, "max_column": 17, "dimensions": "17列 x 419行"}, "merged_cells": [], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "C", "value": 26790, "data_type": "int", "is_merged": false}, {"column": "D", "value": 106632.2, "data_type": "float", "is_merged": false}, {"column": "E", "value": 22390, "data_type": "int", "is_merged": false}, {"column": "F", "value": 319348.99, "data_type": "float", "is_merged": false}, {"column": "G", "value": 28791.6, "data_type": "float", "is_merged": false}, {"column": "H", "value": 322284.3, "data_type": "float", "is_merged": false}, {"column": "I", "value": 20388.4, "data_type": "float", "is_merged": false}, {"column": "J", "value": 103696.89, "data_type": "float", "is_merged": false}, {"column": "K", "value": true, "data_type": "bool", "is_merged": false}, {"column": "L", "value": true, "data_type": "bool", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": "燃料购入", "data_type": "str", "is_merged": false}, {"column": "P", "value": "燃料消耗", "data_type": "str", "is_merged": false}, {"column": "Q", "value": "副食、燃料结余数", "data_type": "str", "is_merged": false}]}, {"row_number": 2, "cells": [{"column": "A", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "C", "value": "期初", "data_type": "str", "is_merged": false}, {"column": "D", "value": 106632.2, "data_type": "float", "is_merged": false}, {"column": "E", "value": "入库", "data_type": "str", "is_merged": false}, {"column": "F", "value": 319348.99, "data_type": "float", "is_merged": false}, {"column": "G", "value": "出库", "data_type": "str", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": "库存", "data_type": "str", "is_merged": false}, {"column": "J", "value": 103696.89, "data_type": "float", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": 0, "data_type": "int", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 3, "cells": [{"column": "A", "value": "物资名称", "data_type": "str", "is_merged": false}, {"column": "B", "value": "分类", "data_type": "str", "is_merged": false}, {"column": "C", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "D", "value": "金额", "data_type": "str", "is_merged": false}, {"column": "E", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "F", "value": "金额", "data_type": "str", "is_merged": false}, {"column": "G", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "H", "value": "金额", "data_type": "str", "is_merged": false}, {"column": "I", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "J", "value": "金额", "data_type": "str", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": "合计", "data_type": "str", "is_merged": false}, {"column": "O", "value": 319348.99, "data_type": "float", "is_merged": false}, {"column": "P", "value": 322284.3, "data_type": "float", "is_merged": false}, {"column": "Q", "value": 103696.89, "data_type": "float", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "B", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "C", "value": 2297, "data_type": "int", "is_merged": false}, {"column": "D", "value": 9647.4, "data_type": "float", "is_merged": false}, {"column": "E", "value": 500, "data_type": "int", "is_merged": false}, {"column": "F", "value": 2100, "data_type": "int", "is_merged": false}, {"column": "G", "value": 828, "data_type": "int", "is_merged": false}, {"column": "H", "value": 3477.6, "data_type": "float", "is_merged": false}, {"column": "I", "value": 1969, "data_type": "int", "is_merged": false}, {"column": "J", "value": 8269.8, "data_type": "float", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": "西红柿", "data_type": "str", "is_merged": false}, {"column": "B", "value": "蔬菜", "data_type": "str", "is_merged": false}, {"column": "C", "value": 15, "data_type": "int", "is_merged": false}, {"column": "D", "value": 75.6, "data_type": "float", "is_merged": false}, {"column": "E", "value": 214, "data_type": "int", "is_merged": false}, {"column": "F", "value": 1044.12, "data_type": "float", "is_merged": false}, {"column": "G", "value": 200, "data_type": "int", "is_merged": false}, {"column": "H", "value": 965.44, "data_type": "float", "is_merged": false}, {"column": "I", "value": 29, "data_type": "int", "is_merged": false}, {"column": "J", "value": 154.28, "data_type": "float", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}]}], "data_start_row": 1, "row_classifications": [{"row_number": 1, "type": "汇总行", "non_empty_count": 7, "has_numeric": true, "has_text": false, "sample_values": [null, null, "26790", "106632.2", "22390"]}, {"row_number": 2, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": [null, null, "期初", "106632.2", "入库"]}, {"row_number": 3, "type": "表头行", "non_empty_count": 9, "has_numeric": false, "has_text": true, "sample_values": ["物资名称", "分类", "数量", "金额", "数量"]}, {"row_number": 4, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["大米", "大米", "2297", "9647.4", "500"]}, {"row_number": 5, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["西红柿", "蔬菜", "15", "75.6", "214"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["白萝卜", "蔬菜", "16", "33.44", "82"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["桂皮", "调味品类", "1", "29.5", "1"]}, {"row_number": 8, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["小米", "调味品类", "18", "151.2", "22"]}, {"row_number": 9, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["糯米", "调味品类", "14", "107.12", "0"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["绿豆", "调味品类", "58", "580", "0"]}, {"row_number": 11, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["冰淇淋粉（香芋）", "调味品类", "1", "26.4", "0"]}, {"row_number": 12, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["冰淇淋粉（草莓）", "调味品类", "1", "26.4", "0"]}, {"row_number": 13, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["冰淇淋粉（香草软）", "调味品类", "1", "26.4", "0"]}, {"row_number": 14, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["面粉", "面粉", "1671", "5376", "750"]}, {"row_number": 15, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["食用油", "植物油", "1260", "9576", "0"]}, {"row_number": 16, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["五花肉", "畜肉", "45", "949.05", "327"]}, {"row_number": 17, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["红薯粉条", "调味品类", "60", "450", "25"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["枸杞", "调味品类", "7", "268.1", "0"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["议价油", "植物油", "351", "6205.68", "0"]}, {"row_number": 20, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["干黄花菜", "调味品类", "7", "389.2", "0"]}], "column_analysis": [{"column": "A", "column_number": 1, "header": null, "most_common_type": "str", "sample_values": ["物资名称", "大米", "西红柿"], "non_empty_count": 8}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "str", "sample_values": ["分类", "大米", "蔬菜"], "non_empty_count": 8}, {"column": "C", "column_number": 3, "header": 26790, "most_common_type": "int", "sample_values": ["26790", "期初", "数量"], "non_empty_count": 10}, {"column": "D", "column_number": 4, "header": 106632.2, "most_common_type": "float", "sample_values": ["106632.2", "106632.2", "金额"], "non_empty_count": 10}, {"column": "E", "column_number": 5, "header": 22390, "most_common_type": "int", "sample_values": ["22390", "入库", "数量"], "non_empty_count": 10}, {"column": "F", "column_number": 6, "header": 319348.99, "most_common_type": "float", "sample_values": ["319348.99", "319348.99", "金额"], "non_empty_count": 10}, {"column": "G", "column_number": 7, "header": 28791.6, "most_common_type": "int", "sample_values": ["28791.6", "出库", "数量"], "non_empty_count": 10}, {"column": "H", "column_number": 8, "header": 322284.3, "most_common_type": "float", "sample_values": ["322284.3", "金额", "3477.6"], "non_empty_count": 9}, {"column": "I", "column_number": 9, "header": 20388.4, "most_common_type": "int", "sample_values": ["20388.4", "库存", "数量"], "non_empty_count": 10}, {"column": "J", "column_number": 10, "header": 103696.89, "most_common_type": "float", "sample_values": ["103696.89", "103696.89", "金额"], "non_empty_count": 10}]}, "分类汇总表": {"basic_info": {"name": "分类汇总表", "max_row": 269, "max_column": 18, "dimensions": "18列 x 269行"}, "merged_cells": [{"range": "A1:O1", "start_cell": "A1", "end_cell": "O1", "value": "分   项   核   算", "rows": 1, "cols": 15}, {"range": "A2:C2", "start_cell": "A2", "end_cell": "C2", "value": "本 月 期初", "rows": 1, "cols": 3}, {"range": "E2:G2", "start_cell": "E2", "end_cell": "G2", "value": "本 月 购 入", "rows": 1, "cols": 3}, {"range": "I2:K2", "start_cell": "I2", "end_cell": "K2", "value": "本 月 出 库", "rows": 1, "cols": 3}, {"range": "M2:O2", "start_cell": "M2", "end_cell": "O2", "value": "本 月 结 余", "rows": 1, "cols": 3}], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "分   项   核   算", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 2, "cells": [{"column": "A", "value": "本 月 期初", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": "本 月 购 入", "data_type": "str", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": "本 月 出 库", "data_type": "str", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": "本 月 结 余", "data_type": "str", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 3, "cells": [{"column": "A", "value": "品 名", "data_type": "str", "is_merged": false}, {"column": "B", "value": "数 量", "data_type": "str", "is_merged": false}, {"column": "C", "value": "金 额", "data_type": "str", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": "品 名", "data_type": "str", "is_merged": false}, {"column": "F", "value": "数 量", "data_type": "str", "is_merged": false}, {"column": "G", "value": "金 额", "data_type": "str", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": "品 名", "data_type": "str", "is_merged": false}, {"column": "J", "value": "数 量", "data_type": "str", "is_merged": false}, {"column": "K", "value": "金 额", "data_type": "str", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": "品 名", "data_type": "str", "is_merged": false}, {"column": "N", "value": "数 量", "data_type": "str", "is_merged": false}, {"column": "O", "value": "金 额", "data_type": "str", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "B", "value": 2297, "data_type": "int", "is_merged": false}, {"column": "C", "value": 9647.4, "data_type": "float", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "F", "value": 500, "data_type": "int", "is_merged": false}, {"column": "G", "value": 2100, "data_type": "int", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "J", "value": 828, "data_type": "int", "is_merged": false}, {"column": "K", "value": 3477.6, "data_type": "float", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": "大米", "data_type": "str", "is_merged": false}, {"column": "N", "value": 1969, "data_type": "int", "is_merged": false}, {"column": "O", "value": 8269.8, "data_type": "float", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": "面粉", "data_type": "str", "is_merged": false}, {"column": "B", "value": 1671, "data_type": "int", "is_merged": false}, {"column": "C", "value": 5376, "data_type": "int", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": "面粉", "data_type": "str", "is_merged": false}, {"column": "F", "value": 750, "data_type": "int", "is_merged": false}, {"column": "G", "value": 2400, "data_type": "int", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": "面粉", "data_type": "str", "is_merged": false}, {"column": "J", "value": 890, "data_type": "int", "is_merged": false}, {"column": "K", "value": 2876.8, "data_type": "float", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": "面粉", "data_type": "str", "is_merged": false}, {"column": "N", "value": 1531, "data_type": "int", "is_merged": false}, {"column": "O", "value": 4899.2, "data_type": "float", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}]}], "data_start_row": 4, "row_classifications": [{"row_number": 1, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["分   项   核   算", null, null, null, null]}, {"row_number": 2, "type": "表头行", "non_empty_count": 3, "has_numeric": false, "has_text": true, "sample_values": ["本 月 期初", null, null, null, "本 月 购 入"]}, {"row_number": 3, "type": "表头行", "non_empty_count": 7, "has_numeric": false, "has_text": true, "sample_values": ["品 名", "数 量", "金 额", null, "品 名"]}, {"row_number": 4, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["大米", "2297", "9647.4", null, "大米"]}, {"row_number": 5, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["面粉", "1671", "5376", null, "面粉"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["畜肉", "303", "9651.36", null, "畜肉"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["禽肉", "103", "2108.64", null, "禽肉"]}, {"row_number": 8, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["蛋类", "362", "3555.46", null, "蛋类"]}, {"row_number": 9, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["水产类", "65", "2268.54", null, "水产类"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["奶及奶制品", "60", "3273.6", null, "奶及奶制品"]}, {"row_number": 11, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["豆类及其制品", "273", "881.24", null, "豆类及其制品"]}, {"row_number": 12, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["植物油", "1611", "15781.68", null, "植物油"]}, {"row_number": 13, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["蔬菜", "459", "2289.81", null, "蔬菜"]}, {"row_number": 14, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["水果", "129", "1218.01", null, "水果"]}, {"row_number": 15, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["菌藻类", "21", "482.3", null, "菌藻类"]}, {"row_number": 16, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["干果", "10", "150", null, "干果"]}, {"row_number": 17, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["饮品类", "63", "3629.43", null, "饮品类"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["调味品类", "2309", "27687.54", null, "调味品类"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["其他", "2177", "7042.44", null, "其他"]}, {"row_number": 20, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["燃料", "14877", "11588.75", null, "燃料"]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "分   项   核   算", "most_common_type": "str", "sample_values": ["大米", "面粉", "畜肉"], "non_empty_count": 10}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "int", "sample_values": ["2297", "1671", "303"], "non_empty_count": 10}, {"column": "C", "column_number": 3, "header": null, "most_common_type": "float", "sample_values": ["9647.4", "5376", "9651.36"], "non_empty_count": 10}, {"column": "E", "column_number": 5, "header": null, "most_common_type": "str", "sample_values": ["大米", "面粉", "畜肉"], "non_empty_count": 10}, {"column": "F", "column_number": 6, "header": null, "most_common_type": "int", "sample_values": ["500", "750", "3022"], "non_empty_count": 10}, {"column": "G", "column_number": 7, "header": null, "most_common_type": "float", "sample_values": ["2100", "2400", "101723.8"], "non_empty_count": 10}, {"column": "I", "column_number": 9, "header": null, "most_common_type": "str", "sample_values": ["大米", "面粉", "畜肉"], "non_empty_count": 10}, {"column": "J", "column_number": 10, "header": null, "most_common_type": "int", "sample_values": ["828", "890", "2858"], "non_empty_count": 10}]}, "逐日消耗登记表_1": {"basic_info": {"name": "逐日消耗登记表_1", "max_row": 27, "max_column": 27, "dimensions": "27列 x 27行"}, "merged_cells": [{"range": "A1:B1", "start_cell": "A1", "end_cell": "B1", "value": "附1-24", "rows": 1, "cols": 2}, {"range": "A2:Y2", "start_cell": "A2", "end_cell": "Y2", "value": "2025年3月份给养消耗登记凭证", "rows": 1, "cols": 25}, {"range": "A3:B3", "start_cell": "A3", "end_cell": "B3", "value": "填制单位：", "rows": 1, "cols": 2}, {"range": "U3:Y3", "start_cell": "U3", "end_cell": "Y3", "value": "单位：千克、元", "rows": 1, "cols": 5}, {"range": "C4:E4", "start_cell": "C4", "end_cell": "E4", "value": 45717, "rows": 1, "cols": 3}, {"range": "F4:H4", "start_cell": "F4", "end_cell": "H4", "value": 45718, "rows": 1, "cols": 3}, {"range": "I4:K4", "start_cell": "I4", "end_cell": "K4", "value": 45719, "rows": 1, "cols": 3}, {"range": "L4:N4", "start_cell": "L4", "end_cell": "N4", "value": 45720, "rows": 1, "cols": 3}, {"range": "O4:Q4", "start_cell": "O4", "end_cell": "Q4", "value": 45721, "rows": 1, "cols": 3}, {"range": "R4:T4", "start_cell": "R4", "end_cell": "T4", "value": 45722, "rows": 1, "cols": 3}, {"range": "U4:W4", "start_cell": "U4", "end_cell": "W4", "value": 45723, "rows": 1, "cols": 3}, {"range": "X4:Y4", "start_cell": "X4", "end_cell": "Y4", "value": "合计", "rows": 1, "cols": 2}, {"range": "A6:B6", "start_cell": "A6", "end_cell": "B6", "value": "就餐人数", "rows": 1, "cols": 2}, {"range": "A25:B25", "start_cell": "A25", "end_cell": "B25", "value": null, "rows": 1, "cols": 2}, {"range": "C25:E25", "start_cell": "C25", "end_cell": "E25", "value": null, "rows": 1, "cols": 3}, {"range": "F25:H25", "start_cell": "F25", "end_cell": "H25", "value": null, "rows": 1, "cols": 3}, {"range": "I25:K25", "start_cell": "I25", "end_cell": "K25", "value": null, "rows": 1, "cols": 3}, {"range": "L25:N25", "start_cell": "L25", "end_cell": "N25", "value": null, "rows": 1, "cols": 3}, {"range": "O25:Q25", "start_cell": "O25", "end_cell": "Q25", "value": null, "rows": 1, "cols": 3}, {"range": "R25:T25", "start_cell": "R25", "end_cell": "T25", "value": null, "rows": 1, "cols": 3}, {"range": "U25:W25", "start_cell": "U25", "end_cell": "W25", "value": null, "rows": 1, "cols": 3}, {"range": "A7:A9", "start_cell": "A7", "end_cell": "A9", "value": "粮\n食", "rows": 3, "cols": 1}, {"range": "A10:A23", "start_cell": "A10", "end_cell": "A23", "value": "副\n\n\n\n\n食", "rows": 14, "cols": 1}, {"range": "A4:B5", "start_cell": "A4", "end_cell": "B5", "value": "日期", "rows": 2, "cols": 2}], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "附1-24", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 2, "cells": [{"column": "A", "value": "2025年3月份给养消耗登记凭证", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 3, "cells": [{"column": "A", "value": "填制单位：", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "U", "value": "单位：千克、元", "data_type": "str", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "日期", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": 45717, "data_type": "int", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": 45718, "data_type": "int", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": 45719, "data_type": "int", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": 45720, "data_type": "int", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": 45721, "data_type": "int", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": 45722, "data_type": "int", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": 45723, "data_type": "int", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": "合计", "data_type": "str", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": "早", "data_type": "str", "is_merged": false}, {"column": "D", "value": "中", "data_type": "str", "is_merged": false}, {"column": "E", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "F", "value": "早", "data_type": "str", "is_merged": false}, {"column": "G", "value": "中", "data_type": "str", "is_merged": false}, {"column": "H", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "I", "value": "早", "data_type": "str", "is_merged": false}, {"column": "J", "value": "中", "data_type": "str", "is_merged": false}, {"column": "K", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "L", "value": "早", "data_type": "str", "is_merged": false}, {"column": "M", "value": "中", "data_type": "str", "is_merged": false}, {"column": "N", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "O", "value": "早", "data_type": "str", "is_merged": false}, {"column": "P", "value": "中", "data_type": "str", "is_merged": false}, {"column": "Q", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "R", "value": "早", "data_type": "str", "is_merged": false}, {"column": "S", "value": "中", "data_type": "str", "is_merged": false}, {"column": "T", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "U", "value": "早", "data_type": "str", "is_merged": false}, {"column": "V", "value": "中", "data_type": "str", "is_merged": false}, {"column": "W", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "X", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "Y", "value": "金额", "data_type": "str", "is_merged": false}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}], "data_start_row": 4, "row_classifications": [{"row_number": 1, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["附1-24", null, null, null, null]}, {"row_number": 2, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["2025年3月份给养消耗登记凭证", null, null, null, null]}, {"row_number": 3, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["填制单位：", null, null, null, null]}, {"row_number": 4, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": ["日期", null, "45717", null, null]}, {"row_number": 5, "type": "表头行", "non_empty_count": 7, "has_numeric": false, "has_text": true, "sample_values": [null, null, "早", "中", "晚"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": ["就餐人数", null, "205", "205", "205"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["粮\n食", "大米", null, "15", "10"]}, {"row_number": 8, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": [null, "面粉", "15", null, "12"]}, {"row_number": 9, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "合计", "15", "15", "22"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": ["副\n\n\n\n\n食", "畜肉", "6", "15", "42"]}, {"row_number": 11, "type": "数据行", "non_empty_count": 3, "has_numeric": true, "has_text": true, "sample_values": [null, "禽肉", null, null, "46"]}, {"row_number": 12, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "蛋类", "22", "13", "9"]}, {"row_number": 13, "type": "数据行", "non_empty_count": 3, "has_numeric": true, "has_text": true, "sample_values": [null, "水产类", null, null, "17"]}, {"row_number": 14, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "奶及奶制品", "11", null, null]}, {"row_number": 15, "type": "数据行", "non_empty_count": 2, "has_numeric": true, "has_text": true, "sample_values": [null, "豆类及其制品", "2", null, null]}, {"row_number": 16, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "植物油", "9", "9", "22.5"]}, {"row_number": 17, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "蔬菜", "54", "53", "44"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 5, "has_numeric": true, "has_text": true, "sample_values": [null, "水果", null, "35", "29"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 3, "has_numeric": true, "has_text": true, "sample_values": [null, "菌藻类", null, "10", null]}, {"row_number": 20, "type": "数据行", "non_empty_count": 2, "has_numeric": true, "has_text": true, "sample_values": [null, "干果", null, "2", null]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "附1-24", "most_common_type": "str", "sample_values": ["日期", "就餐人数", "粮\n食"], "non_empty_count": 4}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "str", "sample_values": ["大米", "面粉", "合计"], "non_empty_count": 7}, {"column": "C", "column_number": 3, "header": null, "most_common_type": "int", "sample_values": ["45717", "早", "205"], "non_empty_count": 7}, {"column": "D", "column_number": 4, "header": null, "most_common_type": "int", "sample_values": ["中", "205", "15"], "non_empty_count": 6}, {"column": "E", "column_number": 5, "header": null, "most_common_type": "int", "sample_values": ["晚", "205", "10"], "non_empty_count": 9}, {"column": "F", "column_number": 6, "header": null, "most_common_type": "int", "sample_values": ["45718", "早", "206"], "non_empty_count": 7}, {"column": "G", "column_number": 7, "header": null, "most_common_type": "int", "sample_values": ["中", "206", "12"], "non_empty_count": 8}, {"column": "H", "column_number": 8, "header": null, "most_common_type": "int", "sample_values": ["晚", "206", "13"], "non_empty_count": 8}, {"column": "I", "column_number": 9, "header": null, "most_common_type": "int", "sample_values": ["45719", "早", "206"], "non_empty_count": 7}, {"column": "J", "column_number": 10, "header": null, "most_common_type": "int", "sample_values": ["中", "206", "15"], "non_empty_count": 9}]}, "逐日消耗登记表_2": {"basic_info": {"name": "逐日消耗登记表_2", "max_row": 29, "max_column": 27, "dimensions": "27列 x 29行"}, "merged_cells": [{"range": "A1:B1", "start_cell": "A1", "end_cell": "B1", "value": "附1-24", "rows": 1, "cols": 2}, {"range": "A2:Y2", "start_cell": "A2", "end_cell": "Y2", "value": "2025年3月份给养消耗登记凭证", "rows": 1, "cols": 25}, {"range": "A3:B3", "start_cell": "A3", "end_cell": "B3", "value": "填制单位：", "rows": 1, "cols": 2}, {"range": "U3:Y3", "start_cell": "U3", "end_cell": "Y3", "value": "单位：千克、元", "rows": 1, "cols": 5}, {"range": "C4:E4", "start_cell": "C4", "end_cell": "E4", "value": 45724, "rows": 1, "cols": 3}, {"range": "F4:H4", "start_cell": "F4", "end_cell": "H4", "value": 45725, "rows": 1, "cols": 3}, {"range": "I4:K4", "start_cell": "I4", "end_cell": "K4", "value": 45726, "rows": 1, "cols": 3}, {"range": "L4:N4", "start_cell": "L4", "end_cell": "N4", "value": 45727, "rows": 1, "cols": 3}, {"range": "O4:Q4", "start_cell": "O4", "end_cell": "Q4", "value": 45728, "rows": 1, "cols": 3}, {"range": "R4:T4", "start_cell": "R4", "end_cell": "T4", "value": 45729, "rows": 1, "cols": 3}, {"range": "U4:W4", "start_cell": "U4", "end_cell": "W4", "value": 45730, "rows": 1, "cols": 3}, {"range": "X4:Y4", "start_cell": "X4", "end_cell": "Y4", "value": "合计", "rows": 1, "cols": 2}, {"range": "A6:B6", "start_cell": "A6", "end_cell": "B6", "value": "就餐人数", "rows": 1, "cols": 2}, {"range": "A25:B25", "start_cell": "A25", "end_cell": "B25", "value": null, "rows": 1, "cols": 2}, {"range": "C25:E25", "start_cell": "C25", "end_cell": "E25", "value": null, "rows": 1, "cols": 3}, {"range": "F25:H25", "start_cell": "F25", "end_cell": "H25", "value": null, "rows": 1, "cols": 3}, {"range": "I25:K25", "start_cell": "I25", "end_cell": "K25", "value": null, "rows": 1, "cols": 3}, {"range": "L25:N25", "start_cell": "L25", "end_cell": "N25", "value": null, "rows": 1, "cols": 3}, {"range": "O25:Q25", "start_cell": "O25", "end_cell": "Q25", "value": null, "rows": 1, "cols": 3}, {"range": "R25:T25", "start_cell": "R25", "end_cell": "T25", "value": null, "rows": 1, "cols": 3}, {"range": "U25:W25", "start_cell": "U25", "end_cell": "W25", "value": null, "rows": 1, "cols": 3}, {"range": "A7:A9", "start_cell": "A7", "end_cell": "A9", "value": "粮\n食", "rows": 3, "cols": 1}, {"range": "A10:A23", "start_cell": "A10", "end_cell": "A23", "value": "副\n\n\n\n\n食", "rows": 14, "cols": 1}, {"range": "A4:B5", "start_cell": "A4", "end_cell": "B5", "value": "日期", "rows": 2, "cols": 2}], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "附1-24", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 2, "cells": [{"column": "A", "value": "2025年3月份给养消耗登记凭证", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 3, "cells": [{"column": "A", "value": "填制单位：", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "U", "value": "单位：千克、元", "data_type": "str", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "日期", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": 45724, "data_type": "int", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": 45725, "data_type": "int", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": 45726, "data_type": "int", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": 45727, "data_type": "int", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": 45728, "data_type": "int", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": 45729, "data_type": "int", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": 45730, "data_type": "int", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": "合计", "data_type": "str", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": "早", "data_type": "str", "is_merged": false}, {"column": "D", "value": "中", "data_type": "str", "is_merged": false}, {"column": "E", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "F", "value": "早", "data_type": "str", "is_merged": false}, {"column": "G", "value": "中", "data_type": "str", "is_merged": false}, {"column": "H", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "I", "value": "早", "data_type": "str", "is_merged": false}, {"column": "J", "value": "中", "data_type": "str", "is_merged": false}, {"column": "K", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "L", "value": "早", "data_type": "str", "is_merged": false}, {"column": "M", "value": "中", "data_type": "str", "is_merged": false}, {"column": "N", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "O", "value": "早", "data_type": "str", "is_merged": false}, {"column": "P", "value": "中", "data_type": "str", "is_merged": false}, {"column": "Q", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "R", "value": "早", "data_type": "str", "is_merged": false}, {"column": "S", "value": "中", "data_type": "str", "is_merged": false}, {"column": "T", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "U", "value": "早", "data_type": "str", "is_merged": false}, {"column": "V", "value": "中", "data_type": "str", "is_merged": false}, {"column": "W", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "X", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "Y", "value": "金额", "data_type": "str", "is_merged": false}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}], "data_start_row": 4, "row_classifications": [{"row_number": 1, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["附1-24", null, null, null, null]}, {"row_number": 2, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["2025年3月份给养消耗登记凭证", null, null, null, null]}, {"row_number": 3, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["填制单位：", null, null, null, null]}, {"row_number": 4, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": ["日期", null, "45724", null, null]}, {"row_number": 5, "type": "表头行", "non_empty_count": 7, "has_numeric": false, "has_text": true, "sample_values": [null, null, "早", "中", "晚"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": ["就餐人数", null, "206", "206", "206"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 5, "has_numeric": true, "has_text": true, "sample_values": ["粮\n食", "大米", null, "12", null]}, {"row_number": 8, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": [null, "面粉", "14", "9", null]}, {"row_number": 9, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "合计", "14", "21", "0"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["副\n\n\n\n\n食", "畜肉", "15", "12", "86"]}, {"row_number": 11, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "禽肉", null, "22", "6"]}, {"row_number": 12, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": [null, "蛋类", "24", "5", null]}, {"row_number": 13, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "水产类", null, "32", "63"]}, {"row_number": 14, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "奶及奶制品", "4", null, null]}, {"row_number": 15, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "豆类及其制品", "12", null, "16"]}, {"row_number": 16, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": [null, "植物油", "9", "9", null]}, {"row_number": 17, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "蔬菜", "44", "91", "44"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 5, "has_numeric": true, "has_text": true, "sample_values": [null, "水果", null, "40", "63"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 3, "has_numeric": true, "has_text": true, "sample_values": [null, "菌藻类", null, null, "13"]}, {"row_number": 20, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": [null, "干果", null, null, null]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "附1-24", "most_common_type": "str", "sample_values": ["日期", "就餐人数", "粮\n食"], "non_empty_count": 4}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "str", "sample_values": ["大米", "面粉", "合计"], "non_empty_count": 7}, {"column": "C", "column_number": 3, "header": null, "most_common_type": "int", "sample_values": ["45724", "早", "206"], "non_empty_count": 7}, {"column": "D", "column_number": 4, "header": null, "most_common_type": "int", "sample_values": ["中", "206", "12"], "non_empty_count": 9}, {"column": "E", "column_number": 5, "header": null, "most_common_type": "int", "sample_values": ["晚", "206", "0"], "non_empty_count": 6}, {"column": "F", "column_number": 6, "header": null, "most_common_type": "int", "sample_values": ["45725", "早", "206"], "non_empty_count": 7}, {"column": "G", "column_number": 7, "header": null, "most_common_type": "int", "sample_values": ["中", "206", "15"], "non_empty_count": 6}, {"column": "H", "column_number": 8, "header": null, "most_common_type": "int", "sample_values": ["晚", "206", "13"], "non_empty_count": 9}, {"column": "I", "column_number": 9, "header": null, "most_common_type": "int", "sample_values": ["45726", "早", "206"], "non_empty_count": 7}, {"column": "J", "column_number": 10, "header": null, "most_common_type": "int", "sample_values": ["中", "206", "14"], "non_empty_count": 8}]}, "逐日消耗登记表_3": {"basic_info": {"name": "逐日消耗登记表_3", "max_row": 29, "max_column": 27, "dimensions": "27列 x 29行"}, "merged_cells": [{"range": "A1:B1", "start_cell": "A1", "end_cell": "B1", "value": "附1-24", "rows": 1, "cols": 2}, {"range": "A2:Y2", "start_cell": "A2", "end_cell": "Y2", "value": "2025年3月份给养消耗登记凭证", "rows": 1, "cols": 25}, {"range": "A3:B3", "start_cell": "A3", "end_cell": "B3", "value": "填制单位：", "rows": 1, "cols": 2}, {"range": "U3:Y3", "start_cell": "U3", "end_cell": "Y3", "value": "单位：千克、元", "rows": 1, "cols": 5}, {"range": "C4:E4", "start_cell": "C4", "end_cell": "E4", "value": 45731, "rows": 1, "cols": 3}, {"range": "F4:H4", "start_cell": "F4", "end_cell": "H4", "value": 45732, "rows": 1, "cols": 3}, {"range": "I4:K4", "start_cell": "I4", "end_cell": "K4", "value": 45733, "rows": 1, "cols": 3}, {"range": "L4:N4", "start_cell": "L4", "end_cell": "N4", "value": 45734, "rows": 1, "cols": 3}, {"range": "O4:Q4", "start_cell": "O4", "end_cell": "Q4", "value": 45735, "rows": 1, "cols": 3}, {"range": "R4:T4", "start_cell": "R4", "end_cell": "T4", "value": 45736, "rows": 1, "cols": 3}, {"range": "U4:W4", "start_cell": "U4", "end_cell": "W4", "value": 45737, "rows": 1, "cols": 3}, {"range": "X4:Y4", "start_cell": "X4", "end_cell": "Y4", "value": "合计", "rows": 1, "cols": 2}, {"range": "A6:B6", "start_cell": "A6", "end_cell": "B6", "value": "就餐人数", "rows": 1, "cols": 2}, {"range": "A25:B25", "start_cell": "A25", "end_cell": "B25", "value": null, "rows": 1, "cols": 2}, {"range": "C25:E25", "start_cell": "C25", "end_cell": "E25", "value": null, "rows": 1, "cols": 3}, {"range": "F25:H25", "start_cell": "F25", "end_cell": "H25", "value": null, "rows": 1, "cols": 3}, {"range": "I25:K25", "start_cell": "I25", "end_cell": "K25", "value": null, "rows": 1, "cols": 3}, {"range": "L25:N25", "start_cell": "L25", "end_cell": "N25", "value": null, "rows": 1, "cols": 3}, {"range": "O25:Q25", "start_cell": "O25", "end_cell": "Q25", "value": null, "rows": 1, "cols": 3}, {"range": "R25:T25", "start_cell": "R25", "end_cell": "T25", "value": null, "rows": 1, "cols": 3}, {"range": "U25:W25", "start_cell": "U25", "end_cell": "W25", "value": null, "rows": 1, "cols": 3}, {"range": "A7:A9", "start_cell": "A7", "end_cell": "A9", "value": "粮\n食", "rows": 3, "cols": 1}, {"range": "A10:A23", "start_cell": "A10", "end_cell": "A23", "value": "副\n\n\n\n\n食", "rows": 14, "cols": 1}, {"range": "A4:B5", "start_cell": "A4", "end_cell": "B5", "value": "日期", "rows": 2, "cols": 2}], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "附1-24", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 2, "cells": [{"column": "A", "value": "2025年3月份给养消耗登记凭证", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 3, "cells": [{"column": "A", "value": "填制单位：", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "U", "value": "单位：千克、元", "data_type": "str", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "日期", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": 45731, "data_type": "int", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": 45732, "data_type": "int", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": 45733, "data_type": "int", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": 45734, "data_type": "int", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": 45735, "data_type": "int", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": 45736, "data_type": "int", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": 45737, "data_type": "int", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": "合计", "data_type": "str", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": "早", "data_type": "str", "is_merged": false}, {"column": "D", "value": "中", "data_type": "str", "is_merged": false}, {"column": "E", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "F", "value": "早", "data_type": "str", "is_merged": false}, {"column": "G", "value": "中", "data_type": "str", "is_merged": false}, {"column": "H", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "I", "value": "早", "data_type": "str", "is_merged": false}, {"column": "J", "value": "中", "data_type": "str", "is_merged": false}, {"column": "K", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "L", "value": "早", "data_type": "str", "is_merged": false}, {"column": "M", "value": "中", "data_type": "str", "is_merged": false}, {"column": "N", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "O", "value": "早", "data_type": "str", "is_merged": false}, {"column": "P", "value": "中", "data_type": "str", "is_merged": false}, {"column": "Q", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "R", "value": "早", "data_type": "str", "is_merged": false}, {"column": "S", "value": "中", "data_type": "str", "is_merged": false}, {"column": "T", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "U", "value": "早", "data_type": "str", "is_merged": false}, {"column": "V", "value": "中", "data_type": "str", "is_merged": false}, {"column": "W", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "X", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "Y", "value": "金额", "data_type": "str", "is_merged": false}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}], "data_start_row": 4, "row_classifications": [{"row_number": 1, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["附1-24", null, null, null, null]}, {"row_number": 2, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["2025年3月份给养消耗登记凭证", null, null, null, null]}, {"row_number": 3, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["填制单位：", null, null, null, null]}, {"row_number": 4, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": ["日期", null, "45731", null, null]}, {"row_number": 5, "type": "表头行", "non_empty_count": 7, "has_numeric": false, "has_text": true, "sample_values": [null, null, "早", "中", "晚"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": ["就餐人数", null, "207", "207", "207"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["粮\n食", "大米", "3", "18", "10"]}, {"row_number": 8, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": [null, "面粉", "15", null, "11"]}, {"row_number": 9, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "合计", "18", "18", "21"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": ["副\n\n\n\n\n食", "畜肉", "41", "11", "32"]}, {"row_number": 11, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": [null, "禽肉", null, null, null]}, {"row_number": 12, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": [null, "蛋类", "14", "4", "7"]}, {"row_number": 13, "type": "数据行", "non_empty_count": 3, "has_numeric": true, "has_text": true, "sample_values": [null, "水产类", null, null, "27"]}, {"row_number": 14, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "奶及奶制品", "22", null, null]}, {"row_number": 15, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "豆类及其制品", "4", "3", null]}, {"row_number": 16, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": [null, "植物油", "9", "9", "9"]}, {"row_number": 17, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": [null, "蔬菜", "32", "38", "77"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "水果", null, "61", "40"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "菌藻类", "4", null, "12"]}, {"row_number": 20, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": [null, "干果", null, null, null]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "附1-24", "most_common_type": "str", "sample_values": ["日期", "就餐人数", "粮\n食"], "non_empty_count": 4}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "str", "sample_values": ["大米", "面粉", "合计"], "non_empty_count": 7}, {"column": "C", "column_number": 3, "header": null, "most_common_type": "int", "sample_values": ["45731", "早", "207"], "non_empty_count": 8}, {"column": "D", "column_number": 4, "header": null, "most_common_type": "int", "sample_values": ["中", "207", "18"], "non_empty_count": 6}, {"column": "E", "column_number": 5, "header": null, "most_common_type": "int", "sample_values": ["晚", "207", "10"], "non_empty_count": 8}, {"column": "F", "column_number": 6, "header": null, "most_common_type": "int", "sample_values": ["45732", "早", "207"], "non_empty_count": 7}, {"column": "G", "column_number": 7, "header": null, "most_common_type": "int", "sample_values": ["中", "207", "0"], "non_empty_count": 3}, {"column": "H", "column_number": 8, "header": null, "most_common_type": "int", "sample_values": ["晚", "207", "16"], "non_empty_count": 8}, {"column": "I", "column_number": 9, "header": null, "most_common_type": "int", "sample_values": ["45733", "早", "205"], "non_empty_count": 7}, {"column": "J", "column_number": 10, "header": null, "most_common_type": "int", "sample_values": ["中", "205", "14"], "non_empty_count": 9}]}, "逐日消耗登记表_4": {"basic_info": {"name": "逐日消耗登记表_4", "max_row": 28, "max_column": 27, "dimensions": "27列 x 28行"}, "merged_cells": [{"range": "A1:B1", "start_cell": "A1", "end_cell": "B1", "value": "附1-24", "rows": 1, "cols": 2}, {"range": "A2:Y2", "start_cell": "A2", "end_cell": "Y2", "value": "2025年3月份给养消耗登记凭证", "rows": 1, "cols": 25}, {"range": "A3:B3", "start_cell": "A3", "end_cell": "B3", "value": "填制单位：", "rows": 1, "cols": 2}, {"range": "U3:Y3", "start_cell": "U3", "end_cell": "Y3", "value": "单位：千克、元", "rows": 1, "cols": 5}, {"range": "C4:E4", "start_cell": "C4", "end_cell": "E4", "value": 45738, "rows": 1, "cols": 3}, {"range": "F4:H4", "start_cell": "F4", "end_cell": "H4", "value": 45739, "rows": 1, "cols": 3}, {"range": "I4:K4", "start_cell": "I4", "end_cell": "K4", "value": 45740, "rows": 1, "cols": 3}, {"range": "L4:N4", "start_cell": "L4", "end_cell": "N4", "value": 45741, "rows": 1, "cols": 3}, {"range": "O4:Q4", "start_cell": "O4", "end_cell": "Q4", "value": 45742, "rows": 1, "cols": 3}, {"range": "R4:T4", "start_cell": "R4", "end_cell": "T4", "value": 45743, "rows": 1, "cols": 3}, {"range": "U4:W4", "start_cell": "U4", "end_cell": "W4", "value": 45744, "rows": 1, "cols": 3}, {"range": "X4:Y4", "start_cell": "X4", "end_cell": "Y4", "value": "合计", "rows": 1, "cols": 2}, {"range": "A6:B6", "start_cell": "A6", "end_cell": "B6", "value": "就餐人数", "rows": 1, "cols": 2}, {"range": "A25:B25", "start_cell": "A25", "end_cell": "B25", "value": null, "rows": 1, "cols": 2}, {"range": "C25:E25", "start_cell": "C25", "end_cell": "E25", "value": null, "rows": 1, "cols": 3}, {"range": "F25:H25", "start_cell": "F25", "end_cell": "H25", "value": null, "rows": 1, "cols": 3}, {"range": "I25:K25", "start_cell": "I25", "end_cell": "K25", "value": null, "rows": 1, "cols": 3}, {"range": "L25:N25", "start_cell": "L25", "end_cell": "N25", "value": null, "rows": 1, "cols": 3}, {"range": "O25:Q25", "start_cell": "O25", "end_cell": "Q25", "value": null, "rows": 1, "cols": 3}, {"range": "R25:T25", "start_cell": "R25", "end_cell": "T25", "value": null, "rows": 1, "cols": 3}, {"range": "U25:W25", "start_cell": "U25", "end_cell": "W25", "value": null, "rows": 1, "cols": 3}, {"range": "A7:A9", "start_cell": "A7", "end_cell": "A9", "value": "粮\n食", "rows": 3, "cols": 1}, {"range": "A10:A23", "start_cell": "A10", "end_cell": "A23", "value": "副\n\n\n\n\n食", "rows": 14, "cols": 1}, {"range": "A4:B5", "start_cell": "A4", "end_cell": "B5", "value": "日期", "rows": 2, "cols": 2}], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "附1-24", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 2, "cells": [{"column": "A", "value": "2025年3月份给养消耗登记凭证", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 3, "cells": [{"column": "A", "value": "填制单位：", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "U", "value": "单位：千克、元", "data_type": "str", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "日期", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": 45738, "data_type": "int", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": 45739, "data_type": "int", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": 45740, "data_type": "int", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": 45741, "data_type": "int", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": 45742, "data_type": "int", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": 45743, "data_type": "int", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": 45744, "data_type": "int", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": "合计", "data_type": "str", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": "早", "data_type": "str", "is_merged": false}, {"column": "D", "value": "中", "data_type": "str", "is_merged": false}, {"column": "E", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "F", "value": "早", "data_type": "str", "is_merged": false}, {"column": "G", "value": "中", "data_type": "str", "is_merged": false}, {"column": "H", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "I", "value": "早", "data_type": "str", "is_merged": false}, {"column": "J", "value": "中", "data_type": "str", "is_merged": false}, {"column": "K", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "L", "value": "早", "data_type": "str", "is_merged": false}, {"column": "M", "value": "中", "data_type": "str", "is_merged": false}, {"column": "N", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "O", "value": "早", "data_type": "str", "is_merged": false}, {"column": "P", "value": "中", "data_type": "str", "is_merged": false}, {"column": "Q", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "R", "value": "早", "data_type": "str", "is_merged": false}, {"column": "S", "value": "中", "data_type": "str", "is_merged": false}, {"column": "T", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "U", "value": "早", "data_type": "str", "is_merged": false}, {"column": "V", "value": "中", "data_type": "str", "is_merged": false}, {"column": "W", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "X", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "Y", "value": "金额", "data_type": "str", "is_merged": false}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}]}], "data_start_row": 4, "row_classifications": [{"row_number": 1, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["附1-24", null, null, null, null]}, {"row_number": 2, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["2025年3月份给养消耗登记凭证", null, null, null, null]}, {"row_number": 3, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["填制单位：", null, null, null, null]}, {"row_number": 4, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": ["日期", null, "45738", null, null]}, {"row_number": 5, "type": "表头行", "non_empty_count": 7, "has_numeric": false, "has_text": true, "sample_values": [null, null, "早", "中", "晚"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": ["就餐人数", null, "206", "206", "206"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": ["粮\n食", "大米", "3", "13", "14"]}, {"row_number": 8, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": [null, "面粉", "8", "11", "10"]}, {"row_number": 9, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "合计", "11", "24", "24"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": ["副\n\n\n\n\n食", "畜肉", "12", null, "38"]}, {"row_number": 11, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "禽肉", null, "26", "22"]}, {"row_number": 12, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": [null, "蛋类", "14", null, "7"]}, {"row_number": 13, "type": "数据行", "non_empty_count": 2, "has_numeric": true, "has_text": true, "sample_values": [null, "水产类", null, "21", null]}, {"row_number": 14, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "奶及奶制品", "6", null, null]}, {"row_number": 15, "type": "数据行", "non_empty_count": 5, "has_numeric": true, "has_text": true, "sample_values": [null, "豆类及其制品", "3", "17", "2"]}, {"row_number": 16, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "植物油", "9", "9", "9"]}, {"row_number": 17, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "蔬菜", "46", "42", "87"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 5, "has_numeric": true, "has_text": true, "sample_values": [null, "水果", null, "43", "31"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 3, "has_numeric": true, "has_text": true, "sample_values": [null, "菌藻类", null, "0.1", null]}, {"row_number": 20, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": [null, "干果", null, null, null]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "附1-24", "most_common_type": "str", "sample_values": ["日期", "就餐人数", "粮\n食"], "non_empty_count": 4}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "str", "sample_values": ["大米", "面粉", "合计"], "non_empty_count": 7}, {"column": "C", "column_number": 3, "header": null, "most_common_type": "int", "sample_values": ["45738", "早", "206"], "non_empty_count": 8}, {"column": "D", "column_number": 4, "header": null, "most_common_type": "int", "sample_values": ["中", "206", "13"], "non_empty_count": 7}, {"column": "E", "column_number": 5, "header": null, "most_common_type": "int", "sample_values": ["晚", "206", "14"], "non_empty_count": 8}, {"column": "F", "column_number": 6, "header": null, "most_common_type": "int", "sample_values": ["45739", "早", "207"], "non_empty_count": 7}, {"column": "G", "column_number": 7, "header": null, "most_common_type": "int", "sample_values": ["中", "207", "12"], "non_empty_count": 6}, {"column": "H", "column_number": 8, "header": null, "most_common_type": "int", "sample_values": ["晚", "207", "13"], "non_empty_count": 7}, {"column": "I", "column_number": 9, "header": null, "most_common_type": "int", "sample_values": ["45740", "早", "205"], "non_empty_count": 7}, {"column": "J", "column_number": 10, "header": null, "most_common_type": "int", "sample_values": ["中", "205", "13"], "non_empty_count": 7}]}, "逐日消耗登记表_5": {"basic_info": {"name": "逐日消耗登记表_5", "max_row": 28, "max_column": 28, "dimensions": "28列 x 28行"}, "merged_cells": [{"range": "A1:B1", "start_cell": "A1", "end_cell": "B1", "value": "附1-24", "rows": 1, "cols": 2}, {"range": "A2:Y2", "start_cell": "A2", "end_cell": "Y2", "value": "2025年3月份给养消耗登记凭证", "rows": 1, "cols": 25}, {"range": "A3:B3", "start_cell": "A3", "end_cell": "B3", "value": "填制单位：", "rows": 1, "cols": 2}, {"range": "U3:Y3", "start_cell": "U3", "end_cell": "Y3", "value": "单位：千克、元", "rows": 1, "cols": 5}, {"range": "C4:E4", "start_cell": "C4", "end_cell": "E4", "value": 45745, "rows": 1, "cols": 3}, {"range": "F4:H4", "start_cell": "F4", "end_cell": "H4", "value": 45746, "rows": 1, "cols": 3}, {"range": "I4:K4", "start_cell": "I4", "end_cell": "K4", "value": 45747, "rows": 1, "cols": 3}, {"range": "L4:N4", "start_cell": "L4", "end_cell": "N4", "value": null, "rows": 1, "cols": 3}, {"range": "O4:Q4", "start_cell": "O4", "end_cell": "Q4", "value": null, "rows": 1, "cols": 3}, {"range": "R4:T4", "start_cell": "R4", "end_cell": "T4", "value": null, "rows": 1, "cols": 3}, {"range": "U4:W4", "start_cell": "U4", "end_cell": "W4", "value": null, "rows": 1, "cols": 3}, {"range": "X4:Y4", "start_cell": "X4", "end_cell": "Y4", "value": "合计", "rows": 1, "cols": 2}, {"range": "A6:B6", "start_cell": "A6", "end_cell": "B6", "value": "就餐人数", "rows": 1, "cols": 2}, {"range": "A25:B25", "start_cell": "A25", "end_cell": "B25", "value": null, "rows": 1, "cols": 2}, {"range": "C25:E25", "start_cell": "C25", "end_cell": "E25", "value": null, "rows": 1, "cols": 3}, {"range": "F25:H25", "start_cell": "F25", "end_cell": "H25", "value": null, "rows": 1, "cols": 3}, {"range": "I25:K25", "start_cell": "I25", "end_cell": "K25", "value": null, "rows": 1, "cols": 3}, {"range": "A7:A9", "start_cell": "A7", "end_cell": "A9", "value": "粮\n食", "rows": 3, "cols": 1}, {"range": "A10:A23", "start_cell": "A10", "end_cell": "A23", "value": "副\n\n\n\n\n食", "rows": 14, "cols": 1}, {"range": "A4:B5", "start_cell": "A4", "end_cell": "B5", "value": "日期", "rows": 2, "cols": 2}], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "附1-24", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AB", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 2, "cells": [{"column": "A", "value": "2025年3月份给养消耗登记凭证", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AB", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 3, "cells": [{"column": "A", "value": "填制单位：", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "F", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "I", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "U", "value": "单位：千克、元", "data_type": "str", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AB", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "日期", "data_type": "str", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": 45745, "data_type": "int", "is_merged": true}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "F", "value": 45746, "data_type": "int", "is_merged": true}, {"column": "G", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "H", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "I", "value": 45747, "data_type": "int", "is_merged": true}, {"column": "J", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "K", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "L", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "M", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "N", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "O", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "P", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Q", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "R", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "S", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "T", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "U", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "V", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "W", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "X", "value": "合计", "data_type": "str", "is_merged": true}, {"column": "Y", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AB", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "B", "value": null, "data_type": "NoneType", "is_merged": true}, {"column": "C", "value": "早", "data_type": "str", "is_merged": false}, {"column": "D", "value": "中", "data_type": "str", "is_merged": false}, {"column": "E", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "F", "value": "早", "data_type": "str", "is_merged": false}, {"column": "G", "value": "中", "data_type": "str", "is_merged": false}, {"column": "H", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "I", "value": "早", "data_type": "str", "is_merged": false}, {"column": "J", "value": "中", "data_type": "str", "is_merged": false}, {"column": "K", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "L", "value": "早", "data_type": "str", "is_merged": false}, {"column": "M", "value": "中", "data_type": "str", "is_merged": false}, {"column": "N", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "O", "value": "早", "data_type": "str", "is_merged": false}, {"column": "P", "value": "中", "data_type": "str", "is_merged": false}, {"column": "Q", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "R", "value": "早", "data_type": "str", "is_merged": false}, {"column": "S", "value": "中", "data_type": "str", "is_merged": false}, {"column": "T", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "U", "value": "早", "data_type": "str", "is_merged": false}, {"column": "V", "value": "中", "data_type": "str", "is_merged": false}, {"column": "W", "value": "晚", "data_type": "str", "is_merged": false}, {"column": "X", "value": "数量", "data_type": "str", "is_merged": false}, {"column": "Y", "value": "金额", "data_type": "str", "is_merged": false}, {"column": "Z", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AA", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "AB", "value": null, "data_type": "NoneType", "is_merged": false}]}], "data_start_row": 4, "row_classifications": [{"row_number": 1, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["附1-24", null, null, null, null]}, {"row_number": 2, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["2025年3月份给养消耗登记凭证", null, null, null, null]}, {"row_number": 3, "type": "标题行", "non_empty_count": 1, "has_numeric": false, "has_text": true, "sample_values": ["填制单位：", null, null, null, null]}, {"row_number": 4, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": ["日期", null, "45745", null, null]}, {"row_number": 5, "type": "表头行", "non_empty_count": 7, "has_numeric": false, "has_text": true, "sample_values": [null, null, "早", "中", "晚"]}, {"row_number": 6, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": ["就餐人数", null, "207", "207", "207"]}, {"row_number": 7, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": ["粮\n食", "大米", null, "12", "14"]}, {"row_number": 8, "type": "数据行", "non_empty_count": 6, "has_numeric": true, "has_text": true, "sample_values": [null, "面粉", "14", null, "12"]}, {"row_number": 9, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "合计", "14", "12", "26"]}, {"row_number": 10, "type": "数据行", "non_empty_count": 9, "has_numeric": true, "has_text": true, "sample_values": ["副\n\n\n\n\n食", "畜肉", "14", "54", "44"]}, {"row_number": 11, "type": "数据行", "non_empty_count": 2, "has_numeric": true, "has_text": true, "sample_values": [null, "禽肉", null, null, "25"]}, {"row_number": 12, "type": "数据行", "non_empty_count": 7, "has_numeric": true, "has_text": true, "sample_values": [null, "蛋类", "24", "5", "5"]}, {"row_number": 13, "type": "数据行", "non_empty_count": 3, "has_numeric": true, "has_text": true, "sample_values": [null, "水产类", null, null, "9"]}, {"row_number": 14, "type": "数据行", "non_empty_count": 5, "has_numeric": true, "has_text": true, "sample_values": [null, "奶及奶制品", "4", null, "19"]}, {"row_number": 15, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "豆类及其制品", "3", "5", null]}, {"row_number": 16, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "植物油", "9", "9", "9"]}, {"row_number": 17, "type": "数据行", "non_empty_count": 8, "has_numeric": true, "has_text": true, "sample_values": [null, "蔬菜", "43", "45", "67"]}, {"row_number": 18, "type": "数据行", "non_empty_count": 5, "has_numeric": true, "has_text": true, "sample_values": [null, "水果", null, "39", "29"]}, {"row_number": 19, "type": "数据行", "non_empty_count": 4, "has_numeric": true, "has_text": true, "sample_values": [null, "菌藻类", null, null, "0.2"]}, {"row_number": 20, "type": "数据行", "non_empty_count": 2, "has_numeric": true, "has_text": true, "sample_values": [null, "干果", null, "2", null]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "附1-24", "most_common_type": "str", "sample_values": ["日期", "就餐人数", "粮\n食"], "non_empty_count": 4}, {"column": "B", "column_number": 2, "header": null, "most_common_type": "str", "sample_values": ["大米", "面粉", "合计"], "non_empty_count": 7}, {"column": "C", "column_number": 3, "header": null, "most_common_type": "int", "sample_values": ["45745", "早", "207"], "non_empty_count": 7}, {"column": "D", "column_number": 4, "header": null, "most_common_type": "int", "sample_values": ["中", "207", "12"], "non_empty_count": 6}, {"column": "E", "column_number": 5, "header": null, "most_common_type": "int", "sample_values": ["晚", "207", "14"], "non_empty_count": 9}, {"column": "F", "column_number": 6, "header": null, "most_common_type": "int", "sample_values": ["45746", "早", "207"], "non_empty_count": 7}, {"column": "G", "column_number": 7, "header": null, "most_common_type": "int", "sample_values": ["中", "207", "14"], "non_empty_count": 6}, {"column": "H", "column_number": 8, "header": null, "most_common_type": "int", "sample_values": ["晚", "207", "15"], "non_empty_count": 7}, {"column": "I", "column_number": 9, "header": null, "most_common_type": "int", "sample_values": ["45747", "早", "207"], "non_empty_count": 7}, {"column": "J", "column_number": 10, "header": null, "most_common_type": "int", "sample_values": ["中", "207", "14"], "non_empty_count": 7}]}, "分类列表": {"basic_info": {"name": "分类列表", "max_row": 1013, "max_column": 5, "dimensions": "5列 x 1013行"}, "merged_cells": [], "first_5_rows": [{"row_number": 1, "cells": [{"column": "A", "value": "物资名称", "data_type": "str", "is_merged": false}, {"column": "B", "value": "分 类", "data_type": "str", "is_merged": false}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 2, "cells": [{"column": "A", "value": "玉米", "data_type": "str", "is_merged": false}, {"column": "B", "value": "蔬菜", "data_type": "str", "is_merged": false}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 3, "cells": [{"column": "A", "value": "五花肉", "data_type": "str", "is_merged": false}, {"column": "B", "value": "畜肉", "data_type": "str", "is_merged": false}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 4, "cells": [{"column": "A", "value": "猪排骨", "data_type": "str", "is_merged": false}, {"column": "B", "value": "畜肉", "data_type": "str", "is_merged": false}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}]}, {"row_number": 5, "cells": [{"column": "A", "value": "猪肘", "data_type": "str", "is_merged": false}, {"column": "B", "value": "畜肉", "data_type": "str", "is_merged": false}, {"column": "C", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "D", "value": null, "data_type": "NoneType", "is_merged": false}, {"column": "E", "value": null, "data_type": "NoneType", "is_merged": false}]}], "data_start_row": 1, "row_classifications": [{"row_number": 1, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["物资名称", "分 类", null, null, null]}, {"row_number": 2, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["玉米", "蔬菜", null, null, null]}, {"row_number": 3, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["五花肉", "畜肉", null, null, null]}, {"row_number": 4, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["猪排骨", "畜肉", null, null, null]}, {"row_number": 5, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["猪肘", "畜肉", null, null, null]}, {"row_number": 6, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["猪里脊", "畜肉", null, null, null]}, {"row_number": 7, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["牛腱子肉", "畜肉", null, null, null]}, {"row_number": 8, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["牛后腿肉", "畜肉", null, null, null]}, {"row_number": 9, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["牛里脊肉", "畜肉", null, null, null]}, {"row_number": 10, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["羊后腿", "畜肉", null, null, null]}, {"row_number": 11, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["羊蹄", "畜肉", null, null, null]}, {"row_number": 12, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["羊拐", "畜肉", null, null, null]}, {"row_number": 13, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["鸡脯肉", "禽肉", null, null, null]}, {"row_number": 14, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["鸽子", "禽肉", null, null, null]}, {"row_number": 15, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["烤鸭", "禽肉", null, null, null]}, {"row_number": 16, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["兔肉", "畜肉", null, null, null]}, {"row_number": 17, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["三黄鸡", "禽肉", null, null, null]}, {"row_number": 18, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["鸡蛋", "蛋类", null, null, null]}, {"row_number": 19, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["鲈鱼", "水产类", null, null, null]}, {"row_number": 20, "type": "表头行", "non_empty_count": 2, "has_numeric": false, "has_text": true, "sample_values": ["虾仁", "水产类", null, null, null]}], "column_analysis": [{"column": "A", "column_number": 1, "header": "物资名称", "most_common_type": "str", "sample_values": ["物资名称", "玉米", "五花肉"], "non_empty_count": 10}, {"column": "B", "column_number": 2, "header": "分 类", "most_common_type": "str", "sample_values": ["分 类", "蔬菜", "畜肉"], "non_empty_count": 10}]}}