# Excel进销存系统结构分析报告

**分析时间**: 2025-06-05 10:51:13
**文件路径**: 2025-3-stock.xlsx

## 工作表概览

| 序号 | 工作表名称 | 行数 | 列数 | 数据开始行 |
|------|------------|------|------|------------|
| 1 | 采购计划 | 10669 | 8 | 4 |
| 2 | 上月库存 | 1590 | 6 | 4 |
| 3 | 库存结余 | 1590 | 6 | 4 |
| 4 | 出入库登记本 | 365 | 159 | 3 |
| 5 | 出库汇总表 | 419 | 17 | 1 |
| 6 | 分类汇总表 | 269 | 18 | 4 |
| 7 | 逐日消耗登记表_1 | 27 | 27 | 4 |
| 8 | 逐日消耗登记表_2 | 29 | 27 | 4 |
| 9 | 逐日消耗登记表_3 | 29 | 27 | 4 |
| 10 | 逐日消耗登记表_4 | 28 | 27 | 4 |
| 11 | 逐日消耗登记表_5 | 28 | 28 | 4 |
| 12 | 分类列表 | 1013 | 5 | 1 |

## 工作表: 采购计划

**基本信息**:
- 维度: 8列 x 10669行
- 数据开始行: 4

**合并单元格**:
- A1:H1: "2025年3月采购计划表" (1行 x 8列)
- A2:F2: "总金额：" (1行 x 6列)
- G2:H2: "319348.99" (1行 x 2列)

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 标题行 | 1 | 2025年3月采购计划表, 空, 空 |
| 2 | 数据行 | 2 | 总金额：, 空, 空 |
| 3 | 表头行 | 8 | 采购日期, 物资名称, 单 位 |
| 4 | 数据行 | 7 | 2025-03-01 00:00:00, 面粉, 千克 |
| 5 | 数据行 | 7 | 2025-03-01 00:00:00, 大米, 千克 |
| 6 | 数据行 | 7 | 2025-03-01 00:00:00, 植物油, 千克 |
| 7 | 数据行 | 7 | 2025-03-03 00:00:00, 五花肉, 千克 |
| 8 | 数据行 | 7 | 2025-03-03 00:00:00, 猪排骨, 千克 |
| 9 | 数据行 | 7 | 2025-03-03 00:00:00, 牛后腿肉, 千克 |
| 10 | 数据行 | 7 | 2025-03-03 00:00:00, 牛排, 千克 |
| 11 | 数据行 | 7 | 2025-03-03 00:00:00, 蹄筋, 千克 |
| 12 | 数据行 | 7 | 2025-03-03 00:00:00, 羊拐, 千克 |
| 13 | 数据行 | 7 | 2025-03-03 00:00:00, 牛腱子肉, 千克 |
| 14 | 数据行 | 7 | 2025-03-03 00:00:00, 鸡脯肉, 千克 |
| 15 | 数据行 | 7 | 2025-03-03 00:00:00, 鸭肠, 千克 |
| 16 | 数据行 | 7 | 2025-03-03 00:00:00, 鸭头, 千克 |
| 17 | 数据行 | 7 | 2025-03-03 00:00:00, 鸭掌, 千克 |
| 18 | 数据行 | 7 | 2025-03-03 00:00:00, 鸡胗, 千克 |
| 19 | 数据行 | 7 | 2025-03-03 00:00:00, 兔肉, 千克 |
| 20 | 数据行 | 7 | 2025-03-03 00:00:00, 三黄鸡, 千克 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 2025年3月采购计划表 | datetime | 10 | 2025-03-01 00:00:00, 2025-03-01 00:00:00, 2025-03-01 00:00:00 |
| B | 无 | str | 10 | 面粉, 大米, 植物油 |
| C | 无 | str | 10 | 千克, 千克, 千克 |
| D | 无 | float | 10 | 3.2, 4.2, 7.6 |
| E | 无 | int | 10 | 750, 500, 720 |
| F | 无 | float | 10 | 2400, 2100, 5472 |
| H | 无 | str | 10 | 面粉, 大米, 植物油 |

## 工作表: 上月库存

**基本信息**:
- 维度: 6列 x 1590行
- 数据开始行: 4

**合并单元格**:
- A1:F1: "2025年2月库存结余表" (1行 x 6列)
- A2:E2: "总金额：" (1行 x 5列)

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 标题行 | 1 | 2025年2月库存结余表, 空, 空 |
| 2 | 数据行 | 2 | 总金额：, 空, 空 |
| 3 | 表头行 | 6 | 日期, 物资名称, 单位 |
| 4 | 数据行 | 6 | 2024-11-15 00:00:00, 大米, 千克 |
| 5 | 数据行 | 6 | 2025-01-01 00:00:00, 大米, 千克 |
| 6 | 数据行 | 6 | 2025-02-27 00:00:00, 大米, 千克 |
| 7 | 数据行 | 6 | 2025-02-28 00:00:00, 西红柿, 千克 |
| 8 | 数据行 | 6 | 2025-02-24 00:00:00, 白萝卜, 千克 |
| 9 | 数据行 | 6 | 2025-02-28 00:00:00, 白萝卜, 千克 |
| 10 | 数据行 | 6 | 2025-02-26 00:00:00, 桂皮, 千克 |
| 11 | 数据行 | 6 | 2024-12-18 00:00:00, 小米, 千克 |
| 12 | 数据行 | 6 | 2025-01-27 00:00:00, 小米, 千克 |
| 13 | 数据行 | 6 | 2025-02-12 00:00:00, 小米, 千克 |
| 14 | 数据行 | 6 | 2025-02-26 00:00:00, 小米, 千克 |
| 15 | 数据行 | 6 | 2024-08-27 00:00:00, 糯米, 千克 |
| 16 | 数据行 | 6 | 2024-12-04 00:00:00, 糯米, 千克 |
| 17 | 数据行 | 6 | 2025-02-26 00:00:00, 糯米, 千克 |
| 18 | 数据行 | 6 | 2024-04-30 00:00:00, 绿豆, 千克 |
| 19 | 数据行 | 6 | 2024-04-30 00:00:00, 冰淇淋粉（香芋）, 千克 |
| 20 | 数据行 | 6 | 2024-04-30 00:00:00, 冰淇淋粉（草莓）, 千克 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 2025年2月库存结余表 | datetime | 10 | 2024-11-15 00:00:00, 2025-01-01 00:00:00, 2025-02-27 00:00:00 |
| B | 无 | str | 10 | 大米, 大米, 大米 |
| C | 无 | str | 10 | 千克, 千克, 千克 |
| D | 无 | float | 10 | 4.2, 4.2, 4.2 |
| E | 无 | int | 10 | 297, 625, 1375 |
| F | 无 | float | 10 | 1247.4, 2625, 5775 |

## 工作表: 库存结余

**基本信息**:
- 维度: 6列 x 1590行
- 数据开始行: 4

**合并单元格**:
- A1:F1: "2025年3月库存结余表" (1行 x 6列)
- A2:E2: "总金额：" (1行 x 5列)

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 标题行 | 1 | 2025年3月库存结余表, 空, 空 |
| 2 | 数据行 | 2 | 总金额：, 空, 空 |
| 3 | 表头行 | 6 | 日期, 物资名称, 单位 |
| 4 | 数据行 | 6 | 2025-01-01 00:00:00, 大米, 千克 |
| 5 | 数据行 | 6 | 2025-02-27 00:00:00, 大米, 千克 |
| 6 | 数据行 | 6 | 2025-03-01 00:00:00, 大米, 千克 |
| 7 | 数据行 | 6 | 2025-03-28 00:00:00, 西红柿, 千克 |
| 8 | 数据行 | 6 | 2025-03-31 00:00:00, 西红柿, 千克 |
| 9 | 数据行 | 6 | 2025-03-21 00:00:00, 白萝卜, 千克 |
| 10 | 数据行 | 6 | 2025-03-24 00:00:00, 白萝卜, 千克 |
| 11 | 数据行 | 6 | 2025-03-26 00:00:00, 白萝卜, 千克 |
| 12 | 数据行 | 6 | 2025-03-28 00:00:00, 白萝卜, 千克 |
| 13 | 数据行 | 6 | 2025-03-31 00:00:00, 白萝卜, 千克 |
| 14 | 数据行 | 6 | 2025-02-26 00:00:00, 小米, 千克 |
| 15 | 数据行 | 6 | 2025-03-12 00:00:00, 小米, 千克 |
| 16 | 数据行 | 6 | 2025-03-26 00:00:00, 小米, 千克 |
| 17 | 数据行 | 6 | 2024-12-04 00:00:00, 糯米, 千克 |
| 18 | 数据行 | 6 | 2025-02-26 00:00:00, 糯米, 千克 |
| 19 | 数据行 | 6 | 2024-04-30 00:00:00, 绿豆, 千克 |
| 20 | 数据行 | 6 | 2024-04-30 00:00:00, 冰淇淋粉（香芋）, 千克 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 2025年3月库存结余表 | datetime | 10 | 2025-01-01 00:00:00, 2025-02-27 00:00:00, 2025-03-01 00:00:00 |
| B | 无 | str | 10 | 大米, 大米, 大米 |
| C | 无 | str | 10 | 千克, 千克, 千克 |
| D | 无 | float | 10 | 4.2, 4.2, 4.2 |
| E | 无 | int | 10 | 94, 1375, 500 |
| F | 无 | float | 10 | 394.8, 5775, 2100 |

## 工作表: 出入库登记本

**基本信息**:
- 维度: 159列 x 365行
- 数据开始行: 3

**合并单元格**:
- A1:AM1: "出入库登记本" (1行 x 39列)
- AN1:BV1: "出入库登记本" (1行 x 35列)
- BW1:DE1: "出入库登记本" (1行 x 35列)
- DF1:EN1: "出入库登记本" (1行 x 35列)
- EO1:FC1: "出入库登记本" (1行 x 15列)
- A2:AM2: "单位：千克、元" (1行 x 39列)
- AN2:BV2: "单位：千克、元" (1行 x 35列)
- BW2:DE2: "单位：千克、元" (1行 x 35列)
- DF2:EN2: "单位：千克、元" (1行 x 35列)
- EO2:FC2: "单位：千克、元" (1行 x 15列)
- E3:I3: "45717" (1行 x 5列)
- J3:N3: "45718" (1行 x 5列)
- O3:S3: "45719" (1行 x 5列)
- T3:X3: "45720" (1行 x 5列)
- Y3:AC3: "45721" (1行 x 5列)
- AD3:AH3: "45722" (1行 x 5列)
- AI3:AM3: "45723" (1行 x 5列)
- AN3:AR3: "45724" (1行 x 5列)
- AS3:AW3: "45725" (1行 x 5列)
- AX3:BB3: "45726" (1行 x 5列)
- BC3:BG3: "45727" (1行 x 5列)
- BH3:BL3: "45728" (1行 x 5列)
- BM3:BQ3: "45729" (1行 x 5列)
- BR3:BV3: "45730" (1行 x 5列)
- BW3:CA3: "45731" (1行 x 5列)
- CB3:CF3: "45732" (1行 x 5列)
- CG3:CK3: "45733" (1行 x 5列)
- CL3:CP3: "45734" (1行 x 5列)
- CQ3:CU3: "45735" (1行 x 5列)
- CV3:CZ3: "45736" (1行 x 5列)
- DA3:DE3: "45737" (1行 x 5列)
- DF3:DJ3: "45738" (1行 x 5列)
- DK3:DO3: "45739" (1行 x 5列)
- DP3:DT3: "45740" (1行 x 5列)
- DU3:DY3: "45741" (1行 x 5列)
- DZ3:ED3: "45742" (1行 x 5列)
- EE3:EI3: "45743" (1行 x 5列)
- EJ3:EN3: "45744" (1行 x 5列)
- EO3:ES3: "45745" (1行 x 5列)
- ET3:EX3: "45746" (1行 x 5列)
- EY3:FC3: "45747" (1行 x 5列)
- A3:A4: "序号" (2行 x 1列)
- B3:B4: "品名" (2行 x 1列)
- C3:C4: "分类" (2行 x 1列)
- D3:D4: "总出库" (2行 x 1列)

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 标题行 | 1 | 出入库登记本, 空, 空 |
| 2 | 标题行 | 1 | 单位：千克、元, 空, 空 |
| 3 | 数据行 | 5 | 序号, 品名, 分类 |
| 4 | 表头行 | 5 | 空, 空, 空 |
| 5 | 数据行 | 9 | 1, 白萝卜, 蔬菜 |
| 6 | 数据行 | 9 | 2, 五花肉, 畜肉 |
| 7 | 数据行 | 9 | 3, 土豆, 蔬菜 |
| 8 | 数据行 | 9 | 4, 大白菜, 蔬菜 |
| 9 | 数据行 | 9 | 5, 黄瓜, 蔬菜 |
| 10 | 数据行 | 9 | 6, 鸡蛋, 蛋类 |
| 11 | 数据行 | 9 | 7, 去皮大蒜, 蔬菜 |
| 12 | 数据行 | 9 | 8, 伊利纯牛奶, 奶及奶制品 |
| 13 | 数据行 | 9 | 9, 清真鸡肉肠, 其他 |
| 14 | 数据行 | 9 | 10, 清真牛肉肠, 其他 |
| 15 | 数据行 | 9 | 11, 松花鸡蛋, 蛋类 |
| 16 | 数据行 | 9 | 12, 乌江海带丝, 其他 |
| 17 | 数据行 | 9 | 13, 羊后腿, 畜肉 |
| 18 | 数据行 | 9 | 14, 毛肚, 畜肉 |
| 19 | 数据行 | 9 | 15, 白象汤好喝桶面, 其他 |
| 20 | 数据行 | 9 | 16, 白象汤好喝袋面, 其他 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 出入库登记本 | int | 9 | 序号, 1, 2 |
| B | 无 | str | 9 | 品名, 白萝卜, 五花肉 |
| C | 无 | str | 9 | 分类, 蔬菜, 畜肉 |
| D | 无 | int | 9 | 总出库, 74, 327 |
| E | 无 | int | 10 | 45717, 入库, 16 |
| F | 无 | int | 9 | 早, 0, 0 |
| G | 无 | int | 9 | 中, 0, 0 |
| H | 无 | int | 9 | 晚, 0, 15 |
| I | 无 | int | 9 | 库存, 16, 30 |
| J | 无 | int | 10 | 45718, 入库, 0 |

## 工作表: 出库汇总表

**基本信息**:
- 维度: 17列 x 419行
- 数据开始行: 1

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 汇总行 | 7 | 空, 空, 26790 |
| 2 | 数据行 | 6 | 空, 空, 期初 |
| 3 | 表头行 | 9 | 物资名称, 分类, 数量 |
| 4 | 数据行 | 9 | 大米, 大米, 2297 |
| 5 | 数据行 | 9 | 西红柿, 蔬菜, 15 |
| 6 | 数据行 | 9 | 白萝卜, 蔬菜, 16 |
| 7 | 数据行 | 9 | 桂皮, 调味品类, 1 |
| 8 | 数据行 | 9 | 小米, 调味品类, 18 |
| 9 | 数据行 | 9 | 糯米, 调味品类, 14 |
| 10 | 数据行 | 9 | 绿豆, 调味品类, 58 |
| 11 | 数据行 | 9 | 冰淇淋粉（香芋）, 调味品类, 1 |
| 12 | 数据行 | 9 | 冰淇淋粉（草莓）, 调味品类, 1 |
| 13 | 数据行 | 9 | 冰淇淋粉（香草软）, 调味品类, 1 |
| 14 | 数据行 | 9 | 面粉, 面粉, 1671 |
| 15 | 数据行 | 9 | 食用油, 植物油, 1260 |
| 16 | 数据行 | 9 | 五花肉, 畜肉, 45 |
| 17 | 数据行 | 9 | 红薯粉条, 调味品类, 60 |
| 18 | 数据行 | 9 | 枸杞, 调味品类, 7 |
| 19 | 数据行 | 9 | 议价油, 植物油, 351 |
| 20 | 数据行 | 9 | 干黄花菜, 调味品类, 7 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 无 | str | 8 | 物资名称, 大米, 西红柿 |
| B | 无 | str | 8 | 分类, 大米, 蔬菜 |
| C | 26790 | int | 10 | 26790, 期初, 数量 |
| D | 106632.2 | float | 10 | 106632.2, 106632.2, 金额 |
| E | 22390 | int | 10 | 22390, 入库, 数量 |
| F | 319348.99 | float | 10 | 319348.99, 319348.99, 金额 |
| G | 28791.6 | int | 10 | 28791.6, 出库, 数量 |
| H | 322284.3 | float | 9 | 322284.3, 金额, 3477.6 |
| I | 20388.4 | int | 10 | 20388.4, 库存, 数量 |
| J | 103696.89 | float | 10 | 103696.89, 103696.89, 金额 |

## 工作表: 分类汇总表

**基本信息**:
- 维度: 18列 x 269行
- 数据开始行: 4

**合并单元格**:
- A1:O1: "分   项   核   算" (1行 x 15列)
- A2:C2: "本 月 期初" (1行 x 3列)
- E2:G2: "本 月 购 入" (1行 x 3列)
- I2:K2: "本 月 出 库" (1行 x 3列)
- M2:O2: "本 月 结 余" (1行 x 3列)

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 标题行 | 1 | 分   项   核   算, 空, 空 |
| 2 | 表头行 | 3 | 本 月 期初, 空, 空 |
| 3 | 表头行 | 7 | 品 名, 数 量, 金 额 |
| 4 | 数据行 | 7 | 大米, 2297, 9647.4 |
| 5 | 数据行 | 7 | 面粉, 1671, 5376 |
| 6 | 数据行 | 7 | 畜肉, 303, 9651.36 |
| 7 | 数据行 | 7 | 禽肉, 103, 2108.64 |
| 8 | 数据行 | 7 | 蛋类, 362, 3555.46 |
| 9 | 数据行 | 7 | 水产类, 65, 2268.54 |
| 10 | 数据行 | 7 | 奶及奶制品, 60, 3273.6 |
| 11 | 数据行 | 7 | 豆类及其制品, 273, 881.24 |
| 12 | 数据行 | 7 | 植物油, 1611, 15781.68 |
| 13 | 数据行 | 7 | 蔬菜, 459, 2289.81 |
| 14 | 数据行 | 7 | 水果, 129, 1218.01 |
| 15 | 数据行 | 7 | 菌藻类, 21, 482.3 |
| 16 | 数据行 | 7 | 干果, 10, 150 |
| 17 | 数据行 | 7 | 饮品类, 63, 3629.43 |
| 18 | 数据行 | 7 | 调味品类, 2309, 27687.54 |
| 19 | 数据行 | 7 | 其他, 2177, 7042.44 |
| 20 | 数据行 | 7 | 燃料, 14877, 11588.75 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 分   项   核   算 | str | 10 | 大米, 面粉, 畜肉 |
| B | 无 | int | 10 | 2297, 1671, 303 |
| C | 无 | float | 10 | 9647.4, 5376, 9651.36 |
| E | 无 | str | 10 | 大米, 面粉, 畜肉 |
| F | 无 | int | 10 | 500, 750, 3022 |
| G | 无 | float | 10 | 2100, 2400, 101723.8 |
| I | 无 | str | 10 | 大米, 面粉, 畜肉 |
| J | 无 | int | 10 | 828, 890, 2858 |

## 工作表: 逐日消耗登记表_1

**基本信息**:
- 维度: 27列 x 27行
- 数据开始行: 4

**合并单元格**:
- A1:B1: "附1-24" (1行 x 2列)
- A2:Y2: "2025年3月份给养消耗登记凭证" (1行 x 25列)
- A3:B3: "填制单位：" (1行 x 2列)
- U3:Y3: "单位：千克、元" (1行 x 5列)
- C4:E4: "45717" (1行 x 3列)
- F4:H4: "45718" (1行 x 3列)
- I4:K4: "45719" (1行 x 3列)
- L4:N4: "45720" (1行 x 3列)
- O4:Q4: "45721" (1行 x 3列)
- R4:T4: "45722" (1行 x 3列)
- U4:W4: "45723" (1行 x 3列)
- X4:Y4: "合计" (1行 x 2列)
- A6:B6: "就餐人数" (1行 x 2列)
- A25:B25: "None" (1行 x 2列)
- C25:E25: "None" (1行 x 3列)
- F25:H25: "None" (1行 x 3列)
- I25:K25: "None" (1行 x 3列)
- L25:N25: "None" (1行 x 3列)
- O25:Q25: "None" (1行 x 3列)
- R25:T25: "None" (1行 x 3列)
- U25:W25: "None" (1行 x 3列)
- A7:A9: "粮
食" (3行 x 1列)
- A10:A23: "副




食" (14行 x 1列)
- A4:B5: "日期" (2行 x 2列)

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 标题行 | 1 | 附1-24, 空, 空 |
| 2 | 标题行 | 1 | 2025年3月份给养消耗登记凭证, 空, 空 |
| 3 | 标题行 | 1 | 填制单位：, 空, 空 |
| 4 | 数据行 | 4 | 日期, 空, 45717 |
| 5 | 表头行 | 7 | 空, 空, 早 |
| 6 | 数据行 | 8 | 就餐人数, 空, 205 |
| 7 | 数据行 | 7 | 粮
食, 大米, 空 |
| 8 | 数据行 | 7 | 空, 面粉, 15 |
| 9 | 数据行 | 8 | 空, 合计, 15 |
| 10 | 数据行 | 8 | 副




食, 畜肉, 6 |
| 11 | 数据行 | 3 | 空, 禽肉, 空 |
| 12 | 数据行 | 8 | 空, 蛋类, 22 |
| 13 | 数据行 | 3 | 空, 水产类, 空 |
| 14 | 数据行 | 4 | 空, 奶及奶制品, 11 |
| 15 | 数据行 | 2 | 空, 豆类及其制品, 2 |
| 16 | 数据行 | 8 | 空, 植物油, 9 |
| 17 | 数据行 | 8 | 空, 蔬菜, 54 |
| 18 | 数据行 | 5 | 空, 水果, 空 |
| 19 | 数据行 | 3 | 空, 菌藻类, 空 |
| 20 | 数据行 | 2 | 空, 干果, 空 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 附1-24 | str | 4 | 日期, 就餐人数, 粮
食 |
| B | 无 | str | 7 | 大米, 面粉, 合计 |
| C | 无 | int | 7 | 45717, 早, 205 |
| D | 无 | int | 6 | 中, 205, 15 |
| E | 无 | int | 9 | 晚, 205, 10 |
| F | 无 | int | 7 | 45718, 早, 206 |
| G | 无 | int | 8 | 中, 206, 12 |
| H | 无 | int | 8 | 晚, 206, 13 |
| I | 无 | int | 7 | 45719, 早, 206 |
| J | 无 | int | 9 | 中, 206, 15 |

## 工作表: 逐日消耗登记表_2

**基本信息**:
- 维度: 27列 x 29行
- 数据开始行: 4

**合并单元格**:
- A1:B1: "附1-24" (1行 x 2列)
- A2:Y2: "2025年3月份给养消耗登记凭证" (1行 x 25列)
- A3:B3: "填制单位：" (1行 x 2列)
- U3:Y3: "单位：千克、元" (1行 x 5列)
- C4:E4: "45724" (1行 x 3列)
- F4:H4: "45725" (1行 x 3列)
- I4:K4: "45726" (1行 x 3列)
- L4:N4: "45727" (1行 x 3列)
- O4:Q4: "45728" (1行 x 3列)
- R4:T4: "45729" (1行 x 3列)
- U4:W4: "45730" (1行 x 3列)
- X4:Y4: "合计" (1行 x 2列)
- A6:B6: "就餐人数" (1行 x 2列)
- A25:B25: "None" (1行 x 2列)
- C25:E25: "None" (1行 x 3列)
- F25:H25: "None" (1行 x 3列)
- I25:K25: "None" (1行 x 3列)
- L25:N25: "None" (1行 x 3列)
- O25:Q25: "None" (1行 x 3列)
- R25:T25: "None" (1行 x 3列)
- U25:W25: "None" (1行 x 3列)
- A7:A9: "粮
食" (3行 x 1列)
- A10:A23: "副




食" (14行 x 1列)
- A4:B5: "日期" (2行 x 2列)

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 标题行 | 1 | 附1-24, 空, 空 |
| 2 | 标题行 | 1 | 2025年3月份给养消耗登记凭证, 空, 空 |
| 3 | 标题行 | 1 | 填制单位：, 空, 空 |
| 4 | 数据行 | 4 | 日期, 空, 45724 |
| 5 | 表头行 | 7 | 空, 空, 早 |
| 6 | 数据行 | 8 | 就餐人数, 空, 206 |
| 7 | 数据行 | 5 | 粮
食, 大米, 空 |
| 8 | 数据行 | 6 | 空, 面粉, 14 |
| 9 | 数据行 | 8 | 空, 合计, 14 |
| 10 | 数据行 | 9 | 副




食, 畜肉, 15 |
| 11 | 数据行 | 4 | 空, 禽肉, 空 |
| 12 | 数据行 | 7 | 空, 蛋类, 24 |
| 13 | 数据行 | 4 | 空, 水产类, 空 |
| 14 | 数据行 | 4 | 空, 奶及奶制品, 4 |
| 15 | 数据行 | 4 | 空, 豆类及其制品, 12 |
| 16 | 数据行 | 7 | 空, 植物油, 9 |
| 17 | 数据行 | 8 | 空, 蔬菜, 44 |
| 18 | 数据行 | 5 | 空, 水果, 空 |
| 19 | 数据行 | 3 | 空, 菌藻类, 空 |
| 20 | 标题行 | 1 | 空, 干果, 空 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 附1-24 | str | 4 | 日期, 就餐人数, 粮
食 |
| B | 无 | str | 7 | 大米, 面粉, 合计 |
| C | 无 | int | 7 | 45724, 早, 206 |
| D | 无 | int | 9 | 中, 206, 12 |
| E | 无 | int | 6 | 晚, 206, 0 |
| F | 无 | int | 7 | 45725, 早, 206 |
| G | 无 | int | 6 | 中, 206, 15 |
| H | 无 | int | 9 | 晚, 206, 13 |
| I | 无 | int | 7 | 45726, 早, 206 |
| J | 无 | int | 8 | 中, 206, 14 |

## 工作表: 逐日消耗登记表_3

**基本信息**:
- 维度: 27列 x 29行
- 数据开始行: 4

**合并单元格**:
- A1:B1: "附1-24" (1行 x 2列)
- A2:Y2: "2025年3月份给养消耗登记凭证" (1行 x 25列)
- A3:B3: "填制单位：" (1行 x 2列)
- U3:Y3: "单位：千克、元" (1行 x 5列)
- C4:E4: "45731" (1行 x 3列)
- F4:H4: "45732" (1行 x 3列)
- I4:K4: "45733" (1行 x 3列)
- L4:N4: "45734" (1行 x 3列)
- O4:Q4: "45735" (1行 x 3列)
- R4:T4: "45736" (1行 x 3列)
- U4:W4: "45737" (1行 x 3列)
- X4:Y4: "合计" (1行 x 2列)
- A6:B6: "就餐人数" (1行 x 2列)
- A25:B25: "None" (1行 x 2列)
- C25:E25: "None" (1行 x 3列)
- F25:H25: "None" (1行 x 3列)
- I25:K25: "None" (1行 x 3列)
- L25:N25: "None" (1行 x 3列)
- O25:Q25: "None" (1行 x 3列)
- R25:T25: "None" (1行 x 3列)
- U25:W25: "None" (1行 x 3列)
- A7:A9: "粮
食" (3行 x 1列)
- A10:A23: "副




食" (14行 x 1列)
- A4:B5: "日期" (2行 x 2列)

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 标题行 | 1 | 附1-24, 空, 空 |
| 2 | 标题行 | 1 | 2025年3月份给养消耗登记凭证, 空, 空 |
| 3 | 标题行 | 1 | 填制单位：, 空, 空 |
| 4 | 数据行 | 4 | 日期, 空, 45731 |
| 5 | 表头行 | 7 | 空, 空, 早 |
| 6 | 数据行 | 8 | 就餐人数, 空, 207 |
| 7 | 数据行 | 6 | 粮
食, 大米, 3 |
| 8 | 数据行 | 6 | 空, 面粉, 15 |
| 9 | 数据行 | 8 | 空, 合计, 18 |
| 10 | 数据行 | 8 | 副




食, 畜肉, 41 |
| 11 | 标题行 | 1 | 空, 禽肉, 空 |
| 12 | 数据行 | 7 | 空, 蛋类, 14 |
| 13 | 数据行 | 3 | 空, 水产类, 空 |
| 14 | 数据行 | 4 | 空, 奶及奶制品, 22 |
| 15 | 数据行 | 4 | 空, 豆类及其制品, 4 |
| 16 | 数据行 | 7 | 空, 植物油, 9 |
| 17 | 数据行 | 7 | 空, 蔬菜, 32 |
| 18 | 数据行 | 4 | 空, 水果, 空 |
| 19 | 数据行 | 4 | 空, 菌藻类, 4 |
| 20 | 标题行 | 1 | 空, 干果, 空 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 附1-24 | str | 4 | 日期, 就餐人数, 粮
食 |
| B | 无 | str | 7 | 大米, 面粉, 合计 |
| C | 无 | int | 8 | 45731, 早, 207 |
| D | 无 | int | 6 | 中, 207, 18 |
| E | 无 | int | 8 | 晚, 207, 10 |
| F | 无 | int | 7 | 45732, 早, 207 |
| G | 无 | int | 3 | 中, 207, 0 |
| H | 无 | int | 8 | 晚, 207, 16 |
| I | 无 | int | 7 | 45733, 早, 205 |
| J | 无 | int | 9 | 中, 205, 14 |

## 工作表: 逐日消耗登记表_4

**基本信息**:
- 维度: 27列 x 28行
- 数据开始行: 4

**合并单元格**:
- A1:B1: "附1-24" (1行 x 2列)
- A2:Y2: "2025年3月份给养消耗登记凭证" (1行 x 25列)
- A3:B3: "填制单位：" (1行 x 2列)
- U3:Y3: "单位：千克、元" (1行 x 5列)
- C4:E4: "45738" (1行 x 3列)
- F4:H4: "45739" (1行 x 3列)
- I4:K4: "45740" (1行 x 3列)
- L4:N4: "45741" (1行 x 3列)
- O4:Q4: "45742" (1行 x 3列)
- R4:T4: "45743" (1行 x 3列)
- U4:W4: "45744" (1行 x 3列)
- X4:Y4: "合计" (1行 x 2列)
- A6:B6: "就餐人数" (1行 x 2列)
- A25:B25: "None" (1行 x 2列)
- C25:E25: "None" (1行 x 3列)
- F25:H25: "None" (1行 x 3列)
- I25:K25: "None" (1行 x 3列)
- L25:N25: "None" (1行 x 3列)
- O25:Q25: "None" (1行 x 3列)
- R25:T25: "None" (1行 x 3列)
- U25:W25: "None" (1行 x 3列)
- A7:A9: "粮
食" (3行 x 1列)
- A10:A23: "副




食" (14行 x 1列)
- A4:B5: "日期" (2行 x 2列)

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 标题行 | 1 | 附1-24, 空, 空 |
| 2 | 标题行 | 1 | 2025年3月份给养消耗登记凭证, 空, 空 |
| 3 | 标题行 | 1 | 填制单位：, 空, 空 |
| 4 | 数据行 | 4 | 日期, 空, 45738 |
| 5 | 表头行 | 7 | 空, 空, 早 |
| 6 | 数据行 | 8 | 就餐人数, 空, 206 |
| 7 | 数据行 | 7 | 粮
食, 大米, 3 |
| 8 | 数据行 | 7 | 空, 面粉, 8 |
| 9 | 数据行 | 8 | 空, 合计, 11 |
| 10 | 数据行 | 8 | 副




食, 畜肉, 12 |
| 11 | 数据行 | 4 | 空, 禽肉, 空 |
| 12 | 数据行 | 6 | 空, 蛋类, 14 |
| 13 | 数据行 | 2 | 空, 水产类, 空 |
| 14 | 数据行 | 4 | 空, 奶及奶制品, 6 |
| 15 | 数据行 | 5 | 空, 豆类及其制品, 3 |
| 16 | 数据行 | 8 | 空, 植物油, 9 |
| 17 | 数据行 | 8 | 空, 蔬菜, 46 |
| 18 | 数据行 | 5 | 空, 水果, 空 |
| 19 | 数据行 | 3 | 空, 菌藻类, 空 |
| 20 | 标题行 | 1 | 空, 干果, 空 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 附1-24 | str | 4 | 日期, 就餐人数, 粮
食 |
| B | 无 | str | 7 | 大米, 面粉, 合计 |
| C | 无 | int | 8 | 45738, 早, 206 |
| D | 无 | int | 7 | 中, 206, 13 |
| E | 无 | int | 8 | 晚, 206, 14 |
| F | 无 | int | 7 | 45739, 早, 207 |
| G | 无 | int | 6 | 中, 207, 12 |
| H | 无 | int | 7 | 晚, 207, 13 |
| I | 无 | int | 7 | 45740, 早, 205 |
| J | 无 | int | 7 | 中, 205, 13 |

## 工作表: 逐日消耗登记表_5

**基本信息**:
- 维度: 28列 x 28行
- 数据开始行: 4

**合并单元格**:
- A1:B1: "附1-24" (1行 x 2列)
- A2:Y2: "2025年3月份给养消耗登记凭证" (1行 x 25列)
- A3:B3: "填制单位：" (1行 x 2列)
- U3:Y3: "单位：千克、元" (1行 x 5列)
- C4:E4: "45745" (1行 x 3列)
- F4:H4: "45746" (1行 x 3列)
- I4:K4: "45747" (1行 x 3列)
- L4:N4: "None" (1行 x 3列)
- O4:Q4: "None" (1行 x 3列)
- R4:T4: "None" (1行 x 3列)
- U4:W4: "None" (1行 x 3列)
- X4:Y4: "合计" (1行 x 2列)
- A6:B6: "就餐人数" (1行 x 2列)
- A25:B25: "None" (1行 x 2列)
- C25:E25: "None" (1行 x 3列)
- F25:H25: "None" (1行 x 3列)
- I25:K25: "None" (1行 x 3列)
- A7:A9: "粮
食" (3行 x 1列)
- A10:A23: "副




食" (14行 x 1列)
- A4:B5: "日期" (2行 x 2列)

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 标题行 | 1 | 附1-24, 空, 空 |
| 2 | 标题行 | 1 | 2025年3月份给养消耗登记凭证, 空, 空 |
| 3 | 标题行 | 1 | 填制单位：, 空, 空 |
| 4 | 数据行 | 4 | 日期, 空, 45745 |
| 5 | 表头行 | 7 | 空, 空, 早 |
| 6 | 数据行 | 8 | 就餐人数, 空, 207 |
| 7 | 数据行 | 6 | 粮
食, 大米, 空 |
| 8 | 数据行 | 6 | 空, 面粉, 14 |
| 9 | 数据行 | 8 | 空, 合计, 14 |
| 10 | 数据行 | 9 | 副




食, 畜肉, 14 |
| 11 | 数据行 | 2 | 空, 禽肉, 空 |
| 12 | 数据行 | 7 | 空, 蛋类, 24 |
| 13 | 数据行 | 3 | 空, 水产类, 空 |
| 14 | 数据行 | 5 | 空, 奶及奶制品, 4 |
| 15 | 数据行 | 4 | 空, 豆类及其制品, 3 |
| 16 | 数据行 | 8 | 空, 植物油, 9 |
| 17 | 数据行 | 8 | 空, 蔬菜, 43 |
| 18 | 数据行 | 5 | 空, 水果, 空 |
| 19 | 数据行 | 4 | 空, 菌藻类, 空 |
| 20 | 数据行 | 2 | 空, 干果, 空 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 附1-24 | str | 4 | 日期, 就餐人数, 粮
食 |
| B | 无 | str | 7 | 大米, 面粉, 合计 |
| C | 无 | int | 7 | 45745, 早, 207 |
| D | 无 | int | 6 | 中, 207, 12 |
| E | 无 | int | 9 | 晚, 207, 14 |
| F | 无 | int | 7 | 45746, 早, 207 |
| G | 无 | int | 6 | 中, 207, 14 |
| H | 无 | int | 7 | 晚, 207, 15 |
| I | 无 | int | 7 | 45747, 早, 207 |
| J | 无 | int | 7 | 中, 207, 14 |

## 工作表: 分类列表

**基本信息**:
- 维度: 5列 x 1013行
- 数据开始行: 1

**行类型分析** (前20行):
| 行号 | 类型 | 非空单元格数 | 示例值 |
|------|------|--------------|--------|
| 1 | 表头行 | 2 | 物资名称, 分 类, 空 |
| 2 | 表头行 | 2 | 玉米, 蔬菜, 空 |
| 3 | 表头行 | 2 | 五花肉, 畜肉, 空 |
| 4 | 表头行 | 2 | 猪排骨, 畜肉, 空 |
| 5 | 表头行 | 2 | 猪肘, 畜肉, 空 |
| 6 | 表头行 | 2 | 猪里脊, 畜肉, 空 |
| 7 | 表头行 | 2 | 牛腱子肉, 畜肉, 空 |
| 8 | 表头行 | 2 | 牛后腿肉, 畜肉, 空 |
| 9 | 表头行 | 2 | 牛里脊肉, 畜肉, 空 |
| 10 | 表头行 | 2 | 羊后腿, 畜肉, 空 |
| 11 | 表头行 | 2 | 羊蹄, 畜肉, 空 |
| 12 | 表头行 | 2 | 羊拐, 畜肉, 空 |
| 13 | 表头行 | 2 | 鸡脯肉, 禽肉, 空 |
| 14 | 表头行 | 2 | 鸽子, 禽肉, 空 |
| 15 | 表头行 | 2 | 烤鸭, 禽肉, 空 |
| 16 | 表头行 | 2 | 兔肉, 畜肉, 空 |
| 17 | 表头行 | 2 | 三黄鸡, 禽肉, 空 |
| 18 | 表头行 | 2 | 鸡蛋, 蛋类, 空 |
| 19 | 表头行 | 2 | 鲈鱼, 水产类, 空 |
| 20 | 表头行 | 2 | 虾仁, 水产类, 空 |

**列结构分析** (前10列):
| 列 | 表头 | 主要数据类型 | 非空数量 | 示例值 |
|----|------|--------------|----------|--------|
| A | 物资名称 | str | 10 | 物资名称, 玉米, 五花肉 |
| B | 分 类 | str | 10 | 分 类, 蔬菜, 畜肉 |

