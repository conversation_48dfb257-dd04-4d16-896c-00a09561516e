Public isChose As Boolean
Private myDate
Sub 合并整理()
    myFiles = getFiles
    If UBound(myFiles) = -1 Then Exit Sub
    Set dic = 读取数据(myFiles)
    With ThisWorkbook
        With .Sheets("采购计划")
            On Error Resume Next
            If .AutoFilterMode Then .ShowAllData
            cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
            On Error GoTo 0
            With .Range(.Cells(4, 1), .Cells(rr, 7))
                .ClearContents
                .Borders.LineStyle = 0
            End With
            rr = 4
            For Each k In dic.Keys
                If dic(k).Exists("采购计划") Then
                    arr = dic(k)("采购计划")
                    .Cells(rr, 1).Resize(UBound(arr, 1), UBound(arr, 2)).Value = arr
                    rr = rr + UBound(arr, 1)
                End If
            Next
            With .Cells(3, 1).Resize(rr, 7)
                .Borders.LineStyle = 1
                .Sort key1:=.Cells(1), Header:=xlYes
            End With
        End With
        With .Sheets("上月库存")
            On Error Resume Next
            If .AutoFilterMode Then .ShowAllData
            cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
            On Error GoTo 0
            With .Range(.Cells(6, 1), .Cells(rr, cc))
                .ClearContents
                .Borders.LineStyle = 0
            End With
            rr = 4
            For Each k In dic.Keys
                If dic(k).Exists("上月库存") Then
                    arr = dic(k)("上月库存")
                    .Cells(rr, 1).Resize(UBound(arr, 1), UBound(arr, 2)).Value = arr
                    rr = rr + UBound(arr, 1)
                End If
            Next
            With .Cells(3, 1).Resize(rr, 6)
                .Borders.LineStyle = 1
                .Sort key1:=.Cells(1), Header:=xlYes
            End With
        End With
        
        With .Sheets("出入库登记本")
            On Error Resume Next
            If .AutoFilterMode Then .ShowAllData
            cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
            On Error GoTo 0
            With .Range(.Cells(6, 1), .Cells(rr, cc))
                .ClearContents
                '.Borders.LineStyle = 0
            End With
            i = 0
            For ac = 4 To cc Step 5
                .Cells(3, ac).Value = CDate(myDate + i)
                i = i + 1
                .Cells(5, ac).ClearContents
            Next
            .Range(.Cells(5, 1), .Cells(5, cc)).Copy .Cells(6, 1).Resize(dic("出入库登记本").Count - 1, cc)
            rr = 5
            For Each k1 In dic("出入库登记本").Keys
                .Cells(rr, 1).Value = rr - 4
                .Cells(rr, 2).Value = k1
                For Each k2 In dic("出入库登记本")(k1).Keys
                    arr = dic("出入库登记本")(k1)(k2)
                    .Cells(rr, k2 * 5).Resize(1, UBound(arr) + 1).Value = arr
                Next
                rr = rr + 1
            Next
'            For r = 5 To rr
'                .Cells(r, 1).Value = rr - 4
'            Next
'            With .Cells(3, 1).Resize(rr, 6)
'                .Borders.LineStyle = 1
'                .Sort key1:=.Cells(1), Header:=xlYes
'            End With
        End With
    End With
    Call main
End Sub
Function 读取数据(myFiles)
    Dim dic As Object
    Set dic = CreateObject("Scripting.Dictionary")
    Set d = CreateObject("Scripting.Dictionary")
    For Each myFile In myFiles
        With Workbooks.Open(myFile, False, True)
            Set dic(myFile) = CreateObject("Scripting.Dictionary")
            With .Sheets("采购计划")
                On Error Resume Next
                If .AutoFilterMode Then .AutoFilterMode = False
                cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
                rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
                On Error GoTo 0
                arr = .Range(.Cells(4, 1), .Cells(rr, 7)).Value
                dic(myFile)(.Name) = arr
            End With
            
            With .Sheets("上月库存")
                On Error Resume Next
                If .AutoFilterMode Then .AutoFilterMode = False
                cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
                rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
                On Error GoTo 0
                arr = .Range(.Cells(4, 1), .Cells(rr, 6)).Value
                dic(myFile)(.Name) = arr
            End With
            With .Sheets("出入库登记本")
                On Error Resume Next
                If .AutoFilterMode Then .AutoFilterMode = False
                cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
                rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
                On Error GoTo 0
                arr = .Range(.Cells(1, 1), .Cells(rr, cc)).Value
                myDate = arr(3, 4)
                For AR = 5 To rr
                    k2 = 0
                    k1 = arr(AR, 2)
                    If Not d.Exists(k1) Then Set d(k1) = CreateObject("Scripting.Dictionary")
                    If k1 <> "" Then
                        For ac = 4 To 158 Step 5
                            k2 = k2 + 1
                            If Not d(k1).Exists(k2) Then d(k1)(k2) = Array(0#, 0#, 0#)
                            crr = d(k1)(k2)
                            For i = 1 To 3
                                If arr(AR, ac + i) <> 0 And arr(AR, ac + i) <> "" Then crr(i - 1) = Round(crr(i - 1) + arr(AR, ac + i), 5)
                            Next
                            d(k1)(k2) = crr
                        Next
                    End If
                Next
            End With
            .Close False
        End With
    Next
    Set dic("出入库登记本") = d
    Set 读取数据 = dic
End Function
Function getFiles(Optional ByVal myTitle As String = "选择一组文件", Optional ByVal fileType As String = "所有Excel文件,*.xls*,所有文件,*")       '文件选择（True是多选）
    If Not isChose Then
        On Error Resume Next
        ChDrive Left(ThisWorkbook.Path, 1)
        ChDir ThisWorkbook.Path
        On Error GoTo 0
        isChose = True
    End If
    getFiles = Application.GetOpenFilename(fileType, , myTitle, , True)
    If TypeName(getFiles) = "Boolean" Then
        getFiles = Array()
    End If
End Function

