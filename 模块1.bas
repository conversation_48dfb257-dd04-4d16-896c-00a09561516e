Sub main()
    Dim tim
    tim = Timer
    Dim iDic As Object
    Set iDic = CreateObject("Scripting.Dictionary")

    Set flDic = 获取分类列表

    ' 调用动态分类管理功能
    Call 动态分类管理

    读取库存 iDic, "上月库存", flDic
    读取库存 iDic, "采购计划", flDic
    Set oDic = 出入库登记本(iDic, flDic)
    Call 出库汇总表(iDic, oDic)
    库存结余 iDic
    Call CG
    Call rk

    MsgBox "完成!!!" & vbCrLf & "耗时" & Format(Timer - tim, "0.00") & "秒"
End Sub

Function addMsg(myMsg, ParamArray oneMsg() As Variant)
    ub = -2
    On Error Resume Next
    ub = UBound(myMsg)
    On Error GoTo 0
    If ub <= -1 Then
        ReDim myMsg(1 To 1 + UBound(oneMsg) - LBound(oneMsg), 1 To 1)
    Else
        ReDim Preserve myMsg(1 To UBound(myMsg, 1), 1 To UBound(myMsg, 2) + 1)
    End If
    ac = UBound(myMsg, 2)
    On Error Resume Next
    For AR = 1 To UBound(myMsg, 1)
        myMsg(AR, ac) = oneMsg(LBound(oneMsg) + AR - 1)
    Next
    On Error GoTo 0
End Function

Sub writeArr(arr, ByVal rngAddress, Optional ByVal shtName As String = "运行报告")
    'arr 请传入1维或2维数组
    Dim sht As Worksheet
    On Error Resume Next
    Set sht = ThisWorkbook.Sheets(shtName)
    On Error GoTo 0
    If sht Is Nothing Then
        Set sht = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
        sht.Name = shtName
    End If
    Set rng = sht.Range(rngAddress)
    clearSht sht, rng.Row, rng.Column
    On Error GoTo skldjflksdjfklsjdflksdjf
    With rng.Resize(UBound(arr, 1) - LBound(arr, 1) + 1, UBound(arr, 2) - LBound(arr, 2) + 1)
        .Value = arr
        .Borders.LineStyle = 1
    End With
    On Error GoTo 0
    Exit Sub
skldjflksdjfklsjdflksdjf:
    With rng.Resize(1, UBound(arr, 1) - LBound(arr, 1) + 1)
        .Value = arr
        .Borders.LineStyle = 1
    End With
    On Error GoTo 0
End Sub
Sub clearSht(sht, Optional ByRef startRow As Long = 2, Optional ByRef startColumn As Long = 1)
    With sht
        If WorksheetFunction.CountA(.UsedRange) > 0 Then
            cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        Else
            rr = 1: cc = 1
        End If
        If rr < startRow Then Exit Sub
        If cc < startColumn Then Exit Sub
        With .Range(.Cells(startRow, startColumn), .Cells(rr, cc))
            .UnMerge
            .ClearContents
            .Borders.LineStyle = 0
            .Interior.Pattern = xlNone
            .Font.ColorIndex = xlAutomatic
        End With
    End With
End Sub

End Sub
Sub 生成下个月的文件()
    'Application.DisplayAlerts = False
    With ThisWorkbook
        With .Sheets("出入库登记本")
            sDate = .Range("E3").Value
            sDate = CDate(sDate)
            sDate = VBA.DateAdd("m", 1, sDate)
            On Error Resume Next
            If .AutoFilterMode Then .ShowAllData
            cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
            On Error GoTo 0
            .Cells(6, 3).Resize(rr, cc).ClearContents
            i = 0
            For ac = 5 To cc Step 5
                tDate = sDate + i
                If Month(tDate) = Month(sDate) Then
                    .Cells(3, ac) = tDate
                Else
                    .Cells(3, ac).MergeArea.ClearContents
                End If
                i = i + 1
            Next
        End With
        With .Sheets("采购计划")
            On Error Resume Next
            If .AutoFilterMode Then .ShowAllData
            cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
            On Error GoTo 0
            .Cells(1, 1).Value = Format(sDate, "yyyy年m月采购计划表")
            .Cells(4, 1).Resize(rr, cc).ClearContents
        End With
        
        With .Sheets("库存结余")
            .Range("A:G").Copy ThisWorkbook.Sheets("上月库存").Range("A:G")
            On Error Resume Next
            If .AutoFilterMode Then .ShowAllData
            cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
            On Error GoTo 0
            .Cells(1, 1).Value = Format(sDate, "yyyy年m月库存结余表")
            .Cells(4, 1).Resize(rr, cc).ClearContents
        End With
        
        With .Sheets("出库汇总表")
            On Error Resume Next
            If .AutoFilterMode Then .ShowAllData
            cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
            On Error GoTo 0
            .Cells(4, 1).Resize(rr, cc).ClearContents
        End With
        
        With .Sheets("分类汇总表")
            On Error Resume Next
            If .AutoFilterMode Then .ShowAllData
            cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
            On Error GoTo 0
            .Cells(4, 2).Resize(rr, cc).ClearContents
        End With
        i = 0
        For x = 1 To 5
            With .Sheets("逐日消耗登记表_" & x)
                On Error Resume Next
                If .AutoFilterMode Then .ShowAllData
                cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
                rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
                On Error GoTo 0
                .Cells(6, 3).Resize(rr, cc).ClearContents
                For ac = 3 To 23 Step 3
                    tDate = sDate + i
                    If Month(tDate) = Month(sDate) Then
                        .Cells(4, ac) = tDate
                    Else
                        .Cells(4, ac).MergeArea.ClearContents
                    End If
                    i = i + 1
                Next
            End With
        Next
        .SaveAs ThisWorkbook.Path & "\" & Format(sDate, "yyyy-m") & "-stock.xlsm"
    End With
End Sub
Sub 库存结余(iDic)
    Dim brr()
    With ThisWorkbook.Sheets("库存结余")
        On Error Resume Next
        If .AutoFilterMode Then .AutoFilterMode = False
        cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        On Error GoTo 0
        With .Cells(4, 1).Resize(rr, cc)
            .UnMerge
            .ClearContents
            .Borders.LineStyle = 0
        End With
        br = 0
        ReDim brr(1 To 6, 1 To 1)
        For Each k In iDic.Keys
            For Each k1 In iDic(k).Keys
                crr = iDic(k)(k1)
                If Round(crr(0), 2) > 0 Then
                    br = br + 1
                    ReDim Preserve brr(1 To UBound(brr, 1), 1 To br)
                    For bc = 1 To UBound(brr, 1)
                        brr(bc, br) = crr(bc + 1)
                    Next
                    brr(5, br) = Round(crr(0), 5)
                    brr(6, br) = Round(crr(1), 5)
                End If
            Next
        Next
        brr = WorksheetFunction.Transpose(brr)
        With .Cells(4, 1).Resize(UBound(brr, 1), UBound(brr, 2))
            .Value = brr
            .Borders.LineStyle = 1
        End With
    End With
End Sub
Sub 分类汇总(brr)
    Dim dic As Object
    Set dic = CreateObject("Scripting.Dictionary")
    With ThisWorkbook.Sheets("分类汇总表")
        On Error Resume Next
        If .AutoFilterMode Then .ShowAllData
        cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        On Error GoTo 0
        .Cells(4, 2).Resize(rr, cc).ClearContents
        arr = .Range(.Cells(4, 1), .Cells(rr, cc)).Value
        ac = 1
        For bc = 3 To 10 Step 2
            dic.RemoveAll
            For br = 1 To UBound(brr, 1)
                k = brr(br, 2)
                v1 = brr(br, bc)
                v2 = brr(br, bc + 1)
                If Not dic.Exists(k) Then dic(k) = Array(0#, 0#)
                crr = dic(k)
                crr(0) = crr(0) + v1
                crr(1) = crr(1) + v2
                dic(k) = crr
            Next
            For AR = 1 To UBound(arr, 1)
                k = arr(AR, 1)
                arr(AR, ac) = k
                If dic.Exists(k) Then
                    crr = dic(k)
                    arr(AR, ac + 1) = Round(crr(0), 5)
                    arr(AR, ac + 2) = Round(crr(1), 5)
                End If
                If k = "主食合计" Then
                    arr(AR, ac + 1) = "=SUM(" & .Cells(4, ac + 1).Resize(2, 1).Address & ")"
                    arr(AR, ac + 2) = "=SUM(" & .Cells(4, ac + 2).Resize(2, 1).Address & ")"
                ElseIf k = "副食合计" Then
                    arr(AR, ac + 1) = "=SUM(" & .Cells(7, ac + 1).Resize(UBound(arr) - 4, 1).Address & ")"
                    arr(AR, ac + 2) = "=SUM(" & .Cells(7, ac + 2).Resize(UBound(arr) - 4, 1).Address & ")"
                End If
            Next
            ac = ac + 4
        Next
        .Cells(4, 1).Resize(UBound(arr, 1), UBound(arr, 2)).Value = arr
    End With
End Sub
Sub 出库汇总表(iDic, oDic)
    sDate = ThisWorkbook.Sheets("出入库登记本").Range("e3").Value
    sDate = CDate(sDate)
    Dim brr()
    ReDim brr(1 To oDic.Count + iDic.Count, 1 To 12)
    br = 0
    For Each k In iDic.Keys
        br = br + 1
        brr(br, 1) = k
        s1 = 0#
        s2 = 0#
        s3 = 0#
        s4 = 0#
        m1 = 0#
        m2 = 0#
        m3 = 0#
        m4 = 0#
        For Each k1 In iDic(k).Keys
            crr = iDic(k)(k1)
            If CDate(crr(2)) < sDate Then
                s1 = s1 + crr(6)
                m1 = m1 + crr(7)
            Else
                s2 = s2 + crr(6)
                m2 = m2 + crr(7)
            End If
            s3 = s3 + crr(6) - crr(0)
            m3 = m3 + crr(7) - crr(1)
            s4 = s4 + crr(0)
            m4 = m4 + crr(1)
            brr(br, 2) = crr(8)
        Next
        brr(br, 3) = Round(s1, 5)
        brr(br, 4) = Round(m1, 5)
        brr(br, 5) = Round(s2, 5)
        brr(br, 6) = Round(m2, 5)
        brr(br, 7) = Round(s3, 5)
        brr(br, 8) = Round(m3, 5)
        brr(br, 9) = Round(s4, 5)
        brr(br, 10) = Round(m4, 5)
        s5 = 0#
        m5 = 0#
        If oDic.Exists(k) Then
            crr = oDic(k)
            s5 = crr(0)
            m5 = crr(1)
            brr(br, 11) = s5
            brr(br, 12) = m5
        End If
    Next
    '    For Each k In oDic.Keys
    '        If Not iDic.Exists(k) Then
    '            br = br + 1
    '            s5 = 0#
    '            m5 = 0#
    '            crr = oDic(k)
    '            s5 = crr(0)
    '            m5 = crr(1)
    '            brr(br, 10) = s5
    '            brr(br, 11) = m5
    '        End If
    '    Next
    
    With ThisWorkbook.Sheets("出库汇总表")
        On Error Resume Next
        If .AutoFilterMode Then .ShowAllData
        cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        On Error GoTo 0
        With .Cells(4, 1).Resize(rr, cc)
            .UnMerge
            .Borders.LineStyle = 0
            .ClearContents
        End With
        With .Cells(4, 1).Resize(br, UBound(brr, 2) - 2)
            .Value = brr
            .Borders.LineStyle = 1
        End With
    End With
    分类汇总 brr
End Sub

Function 获取分类列表()
    Dim dic As Object
    Set dic = CreateObject("Scripting.Dictionary")
    With ThisWorkbook.Sheets("分类列表")
        On Error Resume Next
        If .AutoFilterMode Then .ShowAllData
        cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        On Error GoTo 0
        arr = .Range(.Cells(1, 1), .Cells(rr, cc)).Value
        For AR = 2 To UBound(arr, 1)
            k = arr(AR, 1)
            v = arr(AR, 2)
            If k <> "" And v <> "" Then
                dic(k) = arr(AR, 2)
            Else
                MsgBox "分类列表表格中，第" & AR & "行中，有空白单元格，请检查后继续"
                .Select
                .Cells(AR, 1).Select
                End
            End If
        Next
    End With
    Set 获取分类列表 = dic
End Function
Private Sub 读取库存(dic, shtName, flDic)
    tem = ""
    With ThisWorkbook.Sheets(shtName)
        On Error Resume Next
        If .AutoFilterMode Then .ShowAllData
        cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        On Error GoTo 0
        arr = .Range(.Cells(1, 1), .Cells(rr, cc)).Value
        For AR = 4 To UBound(arr, 1)
            k1 = arr(AR, 2)
            s = arr(AR, 5)
            m = arr(AR, 6)
            If k1 <> "" Then
                If s > 0 And m > 0 Then
                    If Not flDic.Exists(k1) Then
                        tem = tem & k1 & "、"
                        With ThisWorkbook.Sheets("分类列表")
                            .Select
                            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
                            .Cells(rr + 1, 1).Value = k1
                            ' 增强：自动提示用户输入分类
                            Dim 新分类 As String
                            新分类 = InputBox("发现新品名【" & k1 & "】" & vbCrLf & _
                                            "请输入对应的分类：", "新品名分类", "其他")
                            If 新分类 <> "" Then
                                .Cells(rr + 1, 2).Value = 新分类
                            Else
                                .Cells(rr + 1, 2).Value = "其他"
                            End If
                        End With
                    End If
                    If Not dic.Exists(k1) Then
                        Set dic(k1) = CreateObject("Scripting.Dictionary")
                    End If
                    c = dic(k1).Count
                    dic(k1)(c) = Array(s, m, arr(AR, 1), arr(AR, 2), arr(AR, 3), arr(AR, 4), arr(AR, 5), arr(AR, 6), flDic(k1))
                Else
                    MsgBox shtName & "表格中，第" & AR & "行中，有空白单元格，请检查后继续"
                    .Select
                    .Cells(AR, 1).Select
                    End
                End If
            Else
                If s > 0 And m > 0 Then
                    MsgBox shtName & "表格中，第" & AR & "行中，有空白单元格，请检查后继续"
                    .Select
                    .Cells(AR, 1).Select
                    End
                End If
            End If
        Next
    End With
    If tem <> "" Then
        MsgBox tem & "所属分类未添加，请添加后继续"
        End
    End If
End Sub

Function 出入库登记本(iDic, flDic)
    Dim tim
    tim = Timer
    Dim oDic As Object
    Set oDic = CreateObject("Scripting.Dictionary")
    Dim dic As Object
    Set dic = CreateObject("Scripting.Dictionary")
    Dim d As Object
    Set d = CreateObject("Scripting.Dictionary")
    Dim res As Object, res1 As Object, res2 As Object
    Set res = CreateObject("Scripting.Dictionary")
    Set res1 = CreateObject("Scripting.Dictionary")
    Set res2 = CreateObject("Scripting.Dictionary")
    shtName = "出入库登记本"
    With ThisWorkbook.Sheets(shtName)
        On Error Resume Next
        If .AutoFilterMode Then .ShowAllData
        cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        On Error GoTo 0
        arr = .Range(.Cells(1, 1), .Cells(rr, cc)).Value
        For AR = 5 To UBound(arr, 1)
            k = arr(AR, 2)
            d(k) = True
            If Not iDic.Exists(k) Then
                res2(AR) = k
            End If
        Next
        For Each k In iDic.Keys
            If Not d.Exists(k) Then
                dic(k) = rr + dic.Count - 3
            End If
        Next
        If res2.Count > 0 Then
            temArr = res2.Keys
            For i = UBound(temArr) To 0 Step -1
                .Cells(temArr(i), 1).EntireRow.Delete
                'Debug.Print ar
            Next
        End If
        If dic.Count > 0 Then
            arr = dic.Keys
            .Cells(rr + 1, "B").Resize(UBound(arr) + 1, 1).Value = WorksheetFunction.Transpose(arr)
            arr = dic.items
            .Cells(rr + 1, "A").Resize(UBound(arr) + 1, 1).Value = WorksheetFunction.Transpose(arr)
        End If
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        arr = .Range(.Cells(1, 1), .Cells(rr, cc)).Value
        For AR = 5 To rr
            arr(AR, 1) = AR - 4
        Next
        d.RemoveAll
        dic.RemoveAll
        For AR = 4 To UBound(arr, 1)
            If AR = 122 Then
                Debug.Print AR
            End If
            k1 = arr(AR, 2)
            k = flDic(k1)
            If Not d.Exists(k) Then Set d(k) = CreateObject("Scripting.Dictionary")
            If k1 <> "" Then
                总出库 = 0
                For ac = 5 To UBound(arr, 2) Step 5
                    If arr(3, ac) <> "" Then
                        For ri = 1 To 3
                            日出库 = arr(AR, ac + ri)
                            If Not d(k).Exists(CDate(arr(3, ac))) Then
                                d(k)(CDate(arr(3, ac))) = Array(0#, 0#, 0#, 0#)
                            End If
                            If ri = 1 Then drr = d(k)(CDate(arr(3, ac)))
                            If Not (k = "饮品类" Or k = "调味品类" Or k = "其他") Then
                                drr(ri - 1) = drr(ri - 1) + arr(AR, ac + ri)
                            End If
                            总出库 = 总出库 + 日出库
                            需出库 = 日出库
                            日入库 = 0                                                        '单日总共入库的数量（包含期初）
                            临时出库数量 = 0                                                       '临时出库数量累计
                            临时出库金额 = 0#                                                      '临时出库金额累计
                            If iDic.Exists(k1) Then
                                For Each k2 In iDic(k1).Keys
                                    crr = iDic(k1)(k2)
                                    If ac = 4 And ri = 1 Then
                                        If CDate(crr(2)) <= CDate(arr(3, ac)) Then          '如果是第一天，则要加上上个月的库存
                                            日入库 = 日入库 + crr(6)
                                        End If
                                    ElseIf ac > 4 And ri = 1 Then
                                        If CDate(crr(2)) = CDate(arr(3, ac)) Then           '否则入库只计算当日库存
                                            日入库 = 日入库 + crr(6)
                                        End If
                                    End If
                                    If 需出库 > 0 Then
                                        If k = "其他" And CDate(arr(3, ac)) = CDate("2024/4/7") Then
                                            Debug.Print 1
                                        End If
                                        If crr(0) <= 需出库 Then                 '这次库存，不够本次出库，则全部出库
                                            If crr(0) > 0 Then
                                                需出库 = 需出库 - crr(0)              '剩余需出库数量
                                                临时出库数量 = Round(临时出库数量 + crr(0), 5)   '本次已经出库数量
                                                临时出库金额 = Round(临时出库金额 + crr(1), 5)   '本次已经出库金额
                                                drr(3) = Round(drr(3) + crr(1), 5)
                                                crr(0) = 0
                                                crr(1) = 0
                                                iDic(k1)(k2) = crr
                                            End If
                                        Else
                                            单价 = crr(1) / crr(0)    '单价
                                            金额 = Round(需出库 * 单价, 2)      '去除2位小数后的单位
                                            crr(0) = Round(crr(0) - 需出库, 5)
                                            crr(1) = Round(crr(1) - 金额, 5)
                                            临时出库数量 = Round(临时出库数量 + 需出库, 5)
                                            临时出库金额 = Round(临时出库金额 + 金额, 5)
                                            需出库 = 0
                                            iDic(k1)(k2) = crr
                                            drr(3) = Round(drr(3) + 金额, 5)
                                        End If
                                    End If
                                Next
                                If k = "饮品类" Or k = "调味品类" Or k = "其他" Then
                                    drr(ri - 1) = drr(ri - 1) + 临时出库金额
                                End If
                            Else
                                If 日出库 > 0 Then
                                    MsgBox k1 & "未入库，直接出库，程序即将结束，请查看【出入库登记本】行 " & AR
                                    End
                                End If
                            End If
                            If ri = 1 Then
                                arr(AR, ac) = 日入库
                                If ac = 4 Then
                                    arr(AR, ac + 4) = Round(日入库 - 日出库, 2)
                                    If arr(AR, ac + 4) < 0 Then
                                        res1(k1) = arr(AR, ac + 4)
                                    End If
                                Else
                                    arr(AR, ac + 4) = Round(arr(AR, ac - 1) + 日入库 - 日出库, 2)
                                    If arr(AR, ac + 4) < 0 Then
                                        res1(k1) = arr(AR, ac + 4)
                                    End If
                                End If
                                
                            Else
                                arr(AR, ac + 4) = Round(arr(AR, ac + 4) - 日出库, 2)
                            End If
                            If Not dic.Exists(k1) Then
                                dic(k1) = Array(临时出库数量, 临时出库金额, k)
                            Else
                                crr = dic(k1)
                                crr(0) = crr(0) + 临时出库数量
                                crr(1) = crr(1) + 临时出库金额
                                dic(k1) = crr
                            End If
                            If ri = 3 Then d(k)(CDate(arr(3, ac))) = drr
                        Next
                    End If
                Next
                arr(AR, 3) = Round(总出库, 2)
            End If
        Next
        For AR = 5 To rr
            arr(AR, 3) = "=VLOOKUP(B" & AR & ",分类列表!A:B,2,0)"
            arr(AR, 4) = "=SUMIF($E$4:$FC$4,""早"",E" & AR & ":FC" & AR & ")+SUMIF($E$4:$FC$4,""中"",E" & AR & ":FC" & AR & ")+SUMIF($E$4:$FC$4,""晚"",E" & AR & ":FC" & AR & ")"
            arr(AR, 9) = "=E" & AR & "-SUM(F" & AR & ":H" & AR & ")"
            For ac = 14 To cc Step 5
                arr(AR, ac) = "=" & .Cells(AR, ac - 5).Address(False, False) & "+" & .Cells(AR, ac - 4).Address(False, False) & "-SUM(" & .Cells(AR, ac - 3).Resize(1, 3).Address(False, False) & ")"
            Next
        Next
        .Range(.Cells(1, 1), .Cells(rr, cc)).Value = arr
    End With
    Debug.Print Timer() - tim
    Set 出入库登记本 = dic
    逐日消耗登记表 d
'    Dim sp As New ExcelSpice
'    sp.printv d("其他")
'    sp.printv iDic("红牛")
    If res1.Count > 0 Then
        tem = "负库存产品："
        For Each k In res1.Keys
            tem = tem & vbLf & k & "     " & res1(k)
        Next
        MsgBox tem
    End If
End Function
Sub 逐日消耗登记表(dic)
    For Each sht In ThisWorkbook.Sheets
        If sht.Name Like "逐日消耗登记表_*" Then
            With sht
                On Error Resume Next
                If .AutoFilterMode Then .AutoFilterMode = False
                cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
                rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
                On Error GoTo 0
                arr = .Range(.Cells(1, 1), .Cells(rr, cc)).Value
                For AR = 7 To rr
                    k = arr(AR, 2)
                    myS1 = 0#
                    myS2 = 0#
                    If dic.Exists(k) Then
                        For ac = 3 To cc Step 3
                            k1 = arr(4, ac)
                            If IsDate(k1) And k1 <> "" Then
                                arr(AR, ac) = Null
                                arr(AR, ac + 1) = Null
                                arr(AR, ac + 2) = Null
                                If dic(k).Exists(k1) Then
                                    crr = dic(k)(k1)
                                    If crr(0) <> 0 Then arr(AR, ac) = crr(0)
                                    If crr(1) <> 0 Then arr(AR, ac + 1) = crr(1)
                                    If crr(2) <> 0 Then arr(AR, ac + 2) = crr(2)
                                    myS1 = myS1 + WorksheetFunction.Sum(crr(0), crr(1), crr(2))
                                    myS2 = myS2 + crr(3)
                                End If
                            ElseIf k1 = "合计" Then
                                arr(AR, ac) = Round(myS1, 2)
                                arr(AR, ac + 1) = Round(myS2, 2)
                            Else
                                For c = ac To ac + 3
                                    If c <= UBound(arr, 2) Then
                                        arr(AR, c) = Null
                                    End If
                                Next
                            End If
                        Next
                    ElseIf AR = 9 Then
                        For ac = 3 To cc
                            arr(AR, ac) = "=SUM(" & .Cells(AR - 2, ac).Resize(2, 1).Address & ")"
                        Next
                    Else
                        For ac = 3 To cc
                            arr(AR, ac) = Null
                        Next
                    End If
                Next
                .Range(.Cells(1, 1), .Cells(rr, cc)).Value = arr
            End With
        End If
    Next
End Sub

' ========================================
' 简化的品名管理功能
' ========================================

' 手动添加新品名
Sub 添加新品名()
    Dim 品名 As String
    Dim 分类 As String

    品名 = InputBox("请输入新品名：", "添加品名")
    If 品名 = "" Then Exit Sub

    ' 检查是否已存在
    If 检查品名存在(品名) Then
        MsgBox "品名【" & 品名 & "】已存在！", vbInformation
        Exit Sub
    End If

    分类 = InputBox("请输入分类：", "选择分类", "其他")
    If 分类 = "" Then 分类 = "其他"

    ' 添加到分类列表
    With ThisWorkbook.Sheets("分类列表")
        Dim 最后行 As Long
        最后行 = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row + 1
        .Cells(最后行, 1).Value = 品名
        .Cells(最后行, 2).Value = 分类
    End With

    MsgBox "品名【" & 品名 & "】添加成功！", vbInformation

    ' 自动更新分类汇总表结构
    Call 检查并更新分类汇总表结构
End Sub

' 检查品名是否存在
Function 检查品名存在(品名 As String) As Boolean
    With ThisWorkbook.Sheets("分类列表")
        Dim 最后行 As Long
        最后行 = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row

        For i = 2 To 最后行
            If .Cells(i, 1).Value = 品名 Then
                检查品名存在 = True
                Exit Function
            End If
        Next i
    End With
    检查品名存在 = False
End Function

' 智能扫描未分类品名
Sub 扫描未分类品名()
    Dim 未分类品名 As String
    Dim 计数 As Integer

    未分类品名 = ""
    计数 = 0

    ' 扫描出入库登记本
    With ThisWorkbook.Sheets("出入库登记本")
        Dim 最后行 As Long
        最后行 = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row

        For i = 5 To 最后行
            Dim 品名 As String
            品名 = .Cells(i, 2).Value
            If 品名 <> "" And Not 检查品名存在(品名) Then
                未分类品名 = 未分类品名 & 品名 & vbCrLf
                计数 = 计数 + 1
                If 计数 >= 20 Then Exit For ' 最多显示20个
            End If
        Next i
    End With

    If 计数 = 0 Then
        MsgBox "所有品名都已分类！", vbInformation
    Else
        MsgBox "发现 " & 计数 & " 个未分类品名：" & vbCrLf & vbCrLf & 未分类品名, vbInformation
    End If
End Sub

' 数据检查
Sub 检查数据完整性()
    Dim 报告 As String
    Dim 空白行数 As Integer

    报告 = "数据完整性检查报告：" & vbCrLf & vbCrLf
    空白行数 = 0

    ' 检查分类列表
    With ThisWorkbook.Sheets("分类列表")
        Dim 最后行 As Long
        最后行 = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row

        For i = 2 To 最后行
            If .Cells(i, 1).Value = "" Or .Cells(i, 2).Value = "" Then
                空白行数 = 空白行数 + 1
            End If
        Next i

        报告 = 报告 & "分类列表总行数：" & (最后行 - 1) & vbCrLf
        报告 = 报告 & "空白数据行数：" & 空白行数 & vbCrLf
    End With

    MsgBox 报告, vbInformation, "数据检查"
End Sub

' ========================================
' 动态分类管理功能
' ========================================

' 动态分类管理 - 主要功能函数
Sub 动态分类管理()
    On Error GoTo ErrorHandler

    ' 获取分类列表中的所有分类（B列）
    Dim 分类列表分类 As Object
    Set 分类列表分类 = 获取分类列表中的所有分类()

    ' 获取分类汇总表中现有的分类（A列）
    Dim 汇总表分类 As Object
    Set 汇总表分类 = 获取汇总表现有分类()

    ' 检测新增分类
    Dim 新增分类 As Object
    Set 新增分类 = CreateObject("Scripting.Dictionary")

    For Each 分类 In 分类列表分类.Keys
        If Not 汇总表分类.Exists(分类) Then
            新增分类(分类) = True
        End If
    Next

    ' 如果有新分类，自动添加到相关表格
    If 新增分类.Count > 0 Then
        Dim 分类名单 As String
        分类名单 = ""
        For Each 分类 In 新增分类.Keys
            分类名单 = 分类名单 & 分类 & "、"
        Next
        分类名单 = Left(分类名单, Len(分类名单) - 1)

        Dim 回答 As Integer
        回答 = MsgBox("检测到新分类：" & 分类名单 & vbCrLf & vbCrLf & _
                      "是否自动添加到分类汇总表和逐日消耗登记表？", vbYesNo + vbQuestion, "新分类检测")

        If 回答 = vbYes Then
            ' 添加到分类汇总表
            Call 添加新分类到汇总表(新增分类)

            ' 添加到逐日消耗登记表
            Call 添加新分类到逐日消耗登记表(新增分类)

            MsgBox "成功添加 " & 新增分类.Count & " 个新分类到所有相关表格！", vbInformation
        End If
    End If

    Exit Sub

ErrorHandler:
    MsgBox "动态分类管理时发生错误：" & vbCrLf & Err.Description, vbCritical
End Sub

' 获取分类列表中的所有分类（B列）
Function 获取分类列表中的所有分类() As Object
    Dim dic As Object
    Set dic = CreateObject("Scripting.Dictionary")

    With ThisWorkbook.Sheets("分类列表")
        On Error Resume Next
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        On Error GoTo 0

        For i = 2 To rr
            分类名 = Trim(.Cells(i, 2).Value)  ' B列分类
            If 分类名 <> "" Then
                dic(分类名) = True
            End If
        Next i
    End With

    Set 获取分类列表中的所有分类 = dic
End Function

' 获取汇总表现有分类（A列）
Function 获取汇总表现有分类() As Object
    Dim dic As Object
    Set dic = CreateObject("Scripting.Dictionary")

    With ThisWorkbook.Sheets("分类汇总表")
        On Error Resume Next
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        On Error GoTo 0

        ' 从第4行开始读取A列的所有非空分类名称
        For i = 4 To rr
            分类名 = Trim(.Cells(i, 1).Value)  ' A列分类
            ' 只记录非空且不是空白的分类名称
            If 分类名 <> "" And 分类名 <> " " Then
                dic(分类名) = i  ' 记录实际行号位置
            End If
        Next i
    End With

    Set 获取汇总表现有分类 = dic
End Function

' 添加新分类到汇总表
Sub 添加新分类到汇总表(新分类字典 As Object)
    On Error GoTo ErrorHandler

    With ThisWorkbook.Sheets("分类汇总表")
        On Error Resume Next
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
        On Error GoTo 0

        ' 确定插入位置（在表格末尾添加，因为已无合计行）
        Dim 插入位置 As Long
        插入位置 = rr + 1  ' 在最后一行后添加

        ' 添加新分类行
        Dim 当前行 As Long
        当前行 = 插入位置

        For Each 分类 In 新分类字典.Keys
            ' 在A/E/I/M列添加分类名称
            .Cells(当前行, 1).Value = 分类   ' A列-期初分类
            .Cells(当前行, 5).Value = 分类   ' E列-购入分类
            .Cells(当前行, 9).Value = 分类   ' I列-出库分类
            .Cells(当前行, 13).Value = 分类  ' M列-结余分类

            ' 初始化数值列为0
            .Cells(当前行, 2).Value = 0     ' B列-期初数量
            .Cells(当前行, 3).Value = 0     ' C列-期初金额
            .Cells(当前行, 6).Value = 0     ' F列-购入数量
            .Cells(当前行, 7).Value = 0     ' G列-购入金额
            .Cells(当前行, 10).Value = 0    ' J列-出库数量
            .Cells(当前行, 11).Value = 0    ' K列-出库金额
            .Cells(当前行, 14).Value = 0    ' N列-结余数量
            .Cells(当前行, 15).Value = 0    ' O列-结余金额

            ' 复制格式（从上一行复制）
            If 当前行 > 4 And rr >= 4 Then
                .Rows(rr).Copy
                .Rows(当前行).PasteSpecial xlPasteFormats
                Application.CutCopyMode = False
            End If

            当前行 = 当前行 + 1
        Next

    End With
    Exit Sub

ErrorHandler:
    MsgBox "添加新分类到汇总表时发生错误：" & vbCrLf & Err.Description, vbCritical
End Sub

' 添加新分类到逐日消耗登记表
Sub 添加新分类到逐日消耗登记表(新分类字典 As Object)
    On Error GoTo ErrorHandler

    ' 处理逐日消耗登记表_1 到 逐日消耗登记表_5
    For i = 1 To 5
        Dim 工作表名 As String
        工作表名 = "逐日消耗登记表_" & i

        ' 检查工作表是否存在
        If 工作表存在(工作表名) Then
            Call 添加新分类到单个逐日消耗登记表(工作表名, 新分类字典)
        End If
    Next i

    Exit Sub

ErrorHandler:
    MsgBox "添加新分类到逐日消耗登记表时发生错误：" & vbCrLf & Err.Description, vbCritical
End Sub

' 添加新分类到单个逐日消耗登记表
Sub 添加新分类到单个逐日消耗登记表(工作表名 As String, 新分类字典 As Object)
    On Error GoTo ErrorHandler

    With ThisWorkbook.Sheets(工作表名)
        On Error Resume Next
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
        On Error GoTo 0

        ' 获取现有分类（B列）
        Dim 现有分类 As Object
        Set 现有分类 = 获取逐日消耗登记表现有分类(工作表名)

        ' 确定插入位置（在第24行之前，如果没有数据则从第7行开始）
        Dim 插入位置 As Long
        插入位置 = 确定逐日消耗登记表插入位置(工作表名)

        ' 添加新分类行
        Dim 当前行 As Long
        当前行 = 插入位置

        For Each 分类 In 新分类字典.Keys
            If Not 现有分类.Exists(分类) Then
                ' 插入新行
                .Rows(当前行).Insert Shift:=xlDown

                ' 设置B列分类名称
                .Cells(当前行, 2).Value = 分类

                ' 复制第7行的格式和公式结构到新行
                Call 复制逐日消耗登记表行格式(工作表名, 7, 当前行)

                ' 清空数据列（C-Y列），保留公式结构
                Call 初始化逐日消耗登记表数据列(工作表名, 当前行)

                当前行 = 当前行 + 1
            End If
        Next

    End With
    Exit Sub

ErrorHandler:
    MsgBox "添加新分类到" & 工作表名 & "时发生错误：" & vbCrLf & Err.Description, vbCritical
End Sub

' 获取逐日消耗登记表现有分类
Function 获取逐日消耗登记表现有分类(工作表名 As String) As Object
    Dim dic As Object
    Set dic = CreateObject("Scripting.Dictionary")

    With ThisWorkbook.Sheets(工作表名)
        On Error Resume Next
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        On Error GoTo 0

        ' 从第7行开始读取B列的分类名称（跳过合计行）
        For i = 7 To rr
            分类名 = Trim(.Cells(i, 2).Value)
            If 分类名 <> "" And 分类名 <> "合计" Then
                dic(分类名) = i
            End If
        Next i
    End With

    Set 获取逐日消耗登记表现有分类 = dic
End Function

' 确定逐日消耗登记表插入位置
Function 确定逐日消耗登记表插入位置(工作表名 As String) As Long
    With ThisWorkbook.Sheets(工作表名)
        On Error Resume Next
        rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
        On Error GoTo 0

        ' 查找合计行位置，在合计行之前插入
        For i = 7 To rr
            If .Cells(i, 2).Value = "合计" Then
                确定逐日消耗登记表插入位置 = i
                Exit Function
            End If
        Next i

        ' 如果没有找到合计行，在最后添加
        确定逐日消耗登记表插入位置 = rr + 1
    End With
End Function

' 复制逐日消耗登记表行格式
Sub 复制逐日消耗登记表行格式(工作表名 As String, 源行 As Long, 目标行 As Long)
    With ThisWorkbook.Sheets(工作表名)
        ' 复制整行格式
        .Rows(源行).Copy
        .Rows(目标行).PasteSpecial xlPasteFormats
        Application.CutCopyMode = False

        ' 复制边框
        .Rows(目标行).Borders.LineStyle = .Rows(源行).Borders.LineStyle
        .Rows(目标行).Borders.Weight = .Rows(源行).Borders.Weight
    End With
End Sub

' 初始化逐日消耗登记表数据列
Sub 初始化逐日消耗登记表数据列(工作表名 As String, 行号 As Long)
    With ThisWorkbook.Sheets(工作表名)
        On Error Resume Next
        cc = .UsedRange.Find("*", , xlValues, xlWhole, xlByColumns, xlPrevious).Column
        On Error GoTo 0

        ' 清空C列到最后一列的数据，但保留可能的公式结构
        For col = 3 To cc
            ' 检查第4行是否有日期，如果有则清空数据
            If IsDate(.Cells(4, col)) Or .Cells(4, col) = "合计" Then
                .Cells(行号, col).Value = ""
            End If
        Next col
    End With
End Sub

' 检查工作表是否存在
Function 工作表存在(工作表名 As String) As Boolean
    On Error Resume Next
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Sheets(工作表名)
    工作表存在 = (Not ws Is Nothing)
    On Error GoTo 0
End Function





