Sub CG()
Dim arr, i As Long
arr = ThisWorkbook.Sheets("采购计划").Range("a4:h" & ThisWorkbook.Sheets("采购计划").Range("a1048576").End(xlUp).Row)
For i = 1 To UBound(arr)
arr(i, 8) = "=vlookup(b" & CStr(i + 3) & ",分类列表!A:B,2,0)"
Next
ThisWorkbook.Sheets("采购计划").Range("a4:h" & ThisWorkbook.Sheets("采购计划").Range("a1048576").End(xlUp).Row) = arr
End Sub


Sub rk()
Dim arr, brr, crr, i As Long
Dim dic As Object
    Set dic = CreateObject("Scripting.Dictionary")
arr = Sheet3.Range("b4:e" & Sheet3.Range("b1048576").End(xlUp).Row)
brr = Sheet5.Range("b5:b" & Sheet5.Range("b1048576").End(xlUp).Row)
crr = Sheet5.Range("e5:e" & Sheet5.Range("b1048576").End(xlUp).Row)
For i = 1 To UBound(arr)
dic(arr(i, 1)) = dic(arr(i, 1)) + arr(i, 4)
Next
For i = 1 To UBound(brr)
crr(i, 1) = crr(i, 1) + dic(brr(i, 1))
Next
Sheet5.Range("e5:e" & Sheet5.Range("b1048576").End(xlUp).Row) = crr
End Sub

' ========================================
' 品名管理快捷功能
' ========================================

' 快速添加品名（简化版）
Sub 快速添加品名()
    Call 添加新品名
End Sub

' 品名管理菜单
Sub 品名管理菜单()
    Dim 选择 As String
    选择 = InputBox("品名管理功能：" & vbCrLf & _
                   "1 - 添加新品名" & vbCrLf & _
                   "2 - 扫描未分类品名" & vbCrLf & _
                   "3 - 数据完整性检查" & vbCrLf & _
                   "4 - 动态分类管理" & vbCrLf & _
                   "5 - 运行主程序" & vbCrLf & _
                   "6 - 测试分类检测功能" & vbCrLf & _
                   "7 - 检查工作表结构" & vbCrLf & _
                   "8 - 测试逐日消耗登记表功能" & vbCrLf & vbCrLf & _
                   "请输入数字选择：", "品名管理", "1")

    Select Case 选择
        Case "1"
            Call 添加新品名
        Case "2"
            Call 扫描未分类品名
        Case "3"
            Call 检查数据完整性
        Case "4"
            Call 动态分类管理
        Case "5"
            Call main
        Case "6"
            Call 测试分类检测
        Case "7"
            Call 检查工作表结构
        Case "8"
            Call 测试逐日消耗登记表功能
        Case Else
            Exit Sub
    End Select
End Sub

' 快速更新分类汇总表结构
Sub 快速更新分类汇总表()
    Call 动态分类管理
End Sub

' 测试分类检测功能
Sub 测试分类检测()
    MsgBox "开始测试分类检测功能...", vbInformation

    ' 测试获取分类列表
    Dim 分类列表 As Object
    Set 分类列表 = 获取分类列表中的所有分类()

    Dim 信息 As String
    信息 = "分类列表测试结果（B列分类）：" & vbCrLf
    信息 = 信息 & "总分类数：" & 分类列表.Count & vbCrLf & vbCrLf
    信息 = 信息 & "所有分类：" & vbCrLf

    For Each 分类 In 分类列表.Keys
        信息 = 信息 & "- " & 分类 & vbCrLf
    Next

    MsgBox 信息, vbInformation, "分类列表测试"

    ' 测试获取汇总表分类
    Dim 汇总表分类 As Object
    Set 汇总表分类 = 获取汇总表现有分类()

    信息 = "汇总表分类测试结果（A列分类）：" & vbCrLf
    信息 = 信息 & "总分类数：" & 汇总表分类.Count & vbCrLf & vbCrLf
    信息 = 信息 & "所有分类：" & vbCrLf

    For Each 分类 In 汇总表分类.Keys
        信息 = 信息 & "- " & 分类 & " (行号:" & 汇总表分类(分类) & ")" & vbCrLf
    Next

    MsgBox 信息, vbInformation, "汇总表分类测试"
End Sub

' 测试逐日消耗登记表功能
Sub 测试逐日消耗登记表功能()
    MsgBox "开始测试逐日消耗登记表功能...", vbInformation

    Dim 信息 As String
    信息 = "逐日消耗登记表分类测试结果：" & vbCrLf & vbCrLf

    ' 获取分类列表中的所有分类
    Dim 分类列表分类 As Object
    Set 分类列表分类 = 获取分类列表中的所有分类()
    信息 = 信息 & "分类列表总分类数：" & 分类列表分类.Count & vbCrLf & vbCrLf

    ' 测试每个逐日消耗登记表
    Dim 总缺失分类 As Integer
    总缺失分类 = 0

    For i = 1 To 5
        Dim 工作表名 As String
        工作表名 = "逐日消耗登记表_" & i

        If 工作表存在(工作表名) Then
            Dim 表分类 As Object
            Set 表分类 = 获取逐日消耗登记表现有分类(工作表名)

            信息 = 信息 & 工作表名 & "：" & vbCrLf
            信息 = 信息 & "  现有分类数：" & 表分类.Count & vbCrLf

            ' 检查缺失的分类
            Dim 缺失分类 As Integer
            缺失分类 = 0
            For Each 分类 In 分类列表分类.Keys
                If Not 表分类.Exists(分类) Then
                    缺失分类 = 缺失分类 + 1
                End If
            Next

            信息 = 信息 & "  缺失分类数：" & 缺失分类 & vbCrLf
            总缺失分类 = 总缺失分类 + 缺失分类

            ' 显示前5个现有分类
            Dim 计数 As Integer
            计数 = 0
            For Each 分类 In 表分类.Keys
                If 计数 < 5 Then
                    信息 = 信息 & "    - " & 分类 & " (行号:" & 表分类(分类) & ")" & vbCrLf
                    计数 = 计数 + 1
                Else
                    信息 = 信息 & "    ... (还有" & (表分类.Count - 5) & "个分类)" & vbCrLf
                    Exit For
                End If
            Next
            信息 = 信息 & vbCrLf
        Else
            信息 = 信息 & 工作表名 & "：不存在" & vbCrLf & vbCrLf
        End If
    Next i

    信息 = 信息 & "总结：" & vbCrLf
    信息 = 信息 & "- 总缺失分类数：" & 总缺失分类 & vbCrLf
    If 总缺失分类 > 0 Then
        信息 = 信息 & "- 建议运行动态分类管理功能添加缺失分类" & vbCrLf
    Else
        信息 = 信息 & "- 所有逐日消耗登记表的分类都是完整的！" & vbCrLf
    End If

    MsgBox 信息, vbInformation, "逐日消耗登记表测试"
End Sub

' 测试逐日消耗登记表功能
Sub 测试逐日消耗登记表功能()
    MsgBox "开始测试逐日消耗登记表功能...", vbInformation

    Dim 信息 As String
    信息 = "逐日消耗登记表分类测试结果：" & vbCrLf & vbCrLf

    ' 测试每个逐日消耗登记表
    For i = 1 To 5
        Dim 工作表名 As String
        工作表名 = "逐日消耗登记表_" & i

        If 工作表存在(工作表名) Then
            Dim 表分类 As Object
            Set 表分类 = 获取逐日消耗登记表现有分类(工作表名)

            信息 = 信息 & 工作表名 & "：" & vbCrLf
            信息 = 信息 & "  分类数量：" & 表分类.Count & vbCrLf
            信息 = 信息 & "  分类列表：" & vbCrLf

            For Each 分类 In 表分类.Keys
                信息 = 信息 & "    - " & 分类 & " (行号:" & 表分类(分类) & ")" & vbCrLf
            Next
            信息 = 信息 & vbCrLf
        Else
            信息 = 信息 & 工作表名 & "：不存在" & vbCrLf & vbCrLf
        End If
    Next i

    MsgBox 信息, vbInformation, "逐日消耗登记表测试"
End Sub

' 检查工作表结构
Sub 检查工作表结构()
    Dim 信息 As String
    信息 = "工作表结构检查：" & vbCrLf & vbCrLf

    ' 检查分类列表工作表
    On Error Resume Next
    With ThisWorkbook.Sheets("分类列表")
        信息 = 信息 & "分类列表工作表：" & vbCrLf
        信息 = 信息 & "- 工作表存在：" & (Err.Number = 0) & vbCrLf
        If Err.Number = 0 Then
            Dim rr As Long
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
            信息 = 信息 & "- 总行数：" & rr & vbCrLf
            信息 = 信息 & "- A1内容：" & .Cells(1, 1).Value & vbCrLf
            信息 = 信息 & "- B1内容：" & .Cells(1, 2).Value & vbCrLf
            信息 = 信息 & "- 最后一行A列：" & .Cells(rr, 1).Value & vbCrLf
            信息 = 信息 & "- 最后一行B列：" & .Cells(rr, 2).Value & vbCrLf
        End If
    End With
    On Error GoTo 0

    信息 = 信息 & vbCrLf

    ' 检查分类汇总表工作表
    On Error Resume Next
    With ThisWorkbook.Sheets("分类汇总表")
        信息 = 信息 & "分类汇总表工作表：" & vbCrLf
        信息 = 信息 & "- 工作表存在：" & (Err.Number = 0) & vbCrLf
        If Err.Number = 0 Then
            rr = .UsedRange.Find("*", , xlValues, xlWhole, xlByRows, xlPrevious).Row
            信息 = 信息 & "- 总行数：" & rr & vbCrLf
            信息 = 信息 & "- A4内容：" & .Cells(4, 1).Value & vbCrLf
            信息 = 信息 & "- 工作表保护状态：" & .ProtectContents & vbCrLf

            ' 查找副食合计位置
            Dim 副食合计行 As Long
            副食合计行 = 0
            For i = 4 To rr
                If .Cells(i, 1).Value = "副食合计" Then
                    副食合计行 = i
                    Exit For
                End If
            Next i
            信息 = 信息 & "- 副食合计位置：第" & 副食合计行 & "行" & vbCrLf
        End If
    End With
    On Error GoTo 0

    MsgBox 信息, vbInformation, "工作表结构检查"
End Sub


